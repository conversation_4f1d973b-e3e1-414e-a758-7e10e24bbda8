package com.cosfo.mall.bill.service.impl;

import com.cosfo.mall.bill.model.po.BillProfitSharing;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class BillProfitSharingServiceImplTest {
    @Resource
    private BillProfitSharingService billProfitSharingService;

    @Test
    public void queryProfitSharingResult() {
        billProfitSharingService.queryProfitSharingResult(null, null);
    }

    @Test
    public void batchUpdateResult() {
        BillProfitSharing update = new BillProfitSharing();
        update.setStatus(1);
        billProfitSharingService.batchUpdateBillProFitSharing(update, Lists.newArrayList(2900L));
    }
}
package com.cosfo.mall.profitsharing;

import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.service.BillSettlementService;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import com.cosfo.mall.order.converter.RefundConvert;
import com.cosfo.mall.payment.model.dto.RefundAcctSplitDetailDTO;
import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.service.RefundAcctSplitDetailService;
import com.cosfo.mall.payment.service.RefundService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @description: 分账业务测试类
 * @author: George
 * @date: 2024-01-10
 **/
@SpringBootTest
public class ProfitSharingBusinessTestService {

    @Resource
    private ProfitSharingBusinessService profitSharingBusinessService;
    @Resource
    private BillSettlementService billSettlementService;
    @Resource
    private RefundService refundService;
    @Resource
    private RefundAcctSplitDetailService refundAcctSplitDetailService;

    /**
     * 保存分账规则单测
     */
    @Test
    public void testSaveOrderProfitSharingRule() {
        Long orderId = 99870L;
        profitSharingBusinessService.saveOrderProfitSharingRule(orderId);
    }

    @Test
    public void testCalculateProfitSharing() {
        BillProfitSharingOrderDTO billProfitSharingOrderDTO = new BillProfitSharingOrderDTO();
        billProfitSharingOrderDTO.setId(1L);
        billProfitSharingOrderDTO.setTenantId(2L);
        billProfitSharingOrderDTO.setOrderId(99208L);
        profitSharingBusinessService.calculateProfitSharing(billProfitSharingOrderDTO);
    }

    @Test
    public void testGenerateSettlements() {
        Long tenantId = 2L;
        Long orderId = 146560L;
        billSettlementService.generateSettlements(tenantId, Collections.singletonList(orderId));
    }

    @Test
    public void testConfirmRefund() {
        Refund refund = refundService.selectByPrimaryKey(183406L);
        RefundDTO refundDTO = RefundConvert.INSTANCE.toRefundDTO(refund);
        refundDTO.setOrderId(146560L);
        List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOS = refundAcctSplitDetailService.queryByRefundId(refund.getId(), refund.getTenantId());
        refundDTO.setRefundAcctSplitDetailDTOList(refundAcctSplitDetailDTOS);
        refundService.confirmRefund(refundDTO);
    }
}

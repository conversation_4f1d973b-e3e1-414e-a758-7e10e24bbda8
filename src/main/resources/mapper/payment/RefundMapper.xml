<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.payment.mapper.RefundMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.payment.model.po.Refund">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="after_sale_id" jdbcType="BIGINT" property="afterSaleId" />
    <result column="refund_no" jdbcType="VARCHAR" property="refundNo" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="sub_mchid" jdbcType="VARCHAR" property="subMchid" />
    <result column="refund_price" jdbcType="DECIMAL" property="refundPrice" />
    <result column="payment_price" jdbcType="DECIMAL" property="paymentPrice" />
    <result column="refund_id" jdbcType="VARCHAR" property="refundId" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="user_received_account" jdbcType="VARCHAR" property="userReceivedAccount" />
    <result column="success_time" jdbcType="TIMESTAMP" property="successTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="retry_num" jdbcType="INTEGER" property="retryNum" />
    <result column="req_seq_id" jdbcType="VARCHAR" property="reqSeqId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="confirm_refund_req_id" property="confirmRefundReqId"/>
    <result column="payment_id" property="paymentId"/>
    <result column="fee_amount" property="feeAmount"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, after_sale_id, refund_no, refund_status, sub_mchid, refund_price, 
    payment_price, refund_id, channel, user_received_account, success_time, `status`, 
    update_time, create_time, retry_num, req_seq_id, confirm_refund_req_id, payment_id, fee_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from refund
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from refund
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.payment.model.po.Refund" useGeneratedKeys="true">
    insert into refund (tenant_id, after_sale_id, refund_no, 
      refund_status, sub_mchid, refund_price, 
      payment_price, refund_id, channel, 
      user_received_account, success_time, `status`, 
      update_time, create_time, confirm_refund_req_id)
    values (#{tenantId,jdbcType=BIGINT}, #{afterSaleId,jdbcType=BIGINT}, #{refundNo,jdbcType=VARCHAR}, 
      #{refundStatus,jdbcType=INTEGER}, #{subMchid,jdbcType=VARCHAR}, #{refundPrice,jdbcType=DECIMAL}, 
      #{paymentPrice,jdbcType=DECIMAL}, #{refundId,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, 
      #{userReceivedAccount,jdbcType=VARCHAR}, #{successTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{confirmRefundReqId})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.payment.model.po.Refund" useGeneratedKeys="true">
    insert into refund
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="afterSaleId != null">
        after_sale_id,
      </if>
      <if test="refundNo != null">
        refund_no,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="subMchid != null">
        sub_mchid,
      </if>
      <if test="refundPrice != null">
        refund_price,
      </if>
      <if test="paymentPrice != null">
        payment_price,
      </if>
      <if test="refundId != null">
        refund_id,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="userReceivedAccount != null">
        user_received_account,
      </if>
      <if test="successTime != null">
        success_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="reqSeqId != null">
        `req_seq_id`,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="confirmRefundReqId">
        confirm_refund_req_id,
      </if>
      <if test="paymentId">
        payment_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="afterSaleId != null">
        #{afterSaleId,jdbcType=BIGINT},
      </if>
      <if test="refundNo != null">
        #{refundNo,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="subMchid != null">
        #{subMchid,jdbcType=VARCHAR},
      </if>
      <if test="refundPrice != null">
        #{refundPrice,jdbcType=DECIMAL},
      </if>
      <if test="paymentPrice != null">
        #{paymentPrice,jdbcType=DECIMAL},
      </if>
      <if test="refundId != null">
        #{refundId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="userReceivedAccount != null">
        #{userReceivedAccount,jdbcType=VARCHAR},
      </if>
      <if test="successTime != null">
        #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="reqSeqId != null">
        #{reqSeqId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmRefundReqId != null">
        #{confirmRefundReqId},
      </if>
      <if test="paymentId != null">
        #{paymentId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.payment.model.po.Refund">
    update refund
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="afterSaleId != null">
        after_sale_id = #{afterSaleId,jdbcType=BIGINT},
      </if>
      <if test="refundNo != null">
        refund_no = #{refundNo,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="subMchid != null">
        sub_mchid = #{subMchid,jdbcType=VARCHAR},
      </if>
      <if test="refundPrice != null">
        refund_price = #{refundPrice,jdbcType=DECIMAL},
      </if>
      <if test="paymentPrice != null">
        payment_price = #{paymentPrice,jdbcType=DECIMAL},
      </if>
      <if test="refundId != null">
        refund_id = #{refundId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="userReceivedAccount != null">
        user_received_account = #{userReceivedAccount,jdbcType=VARCHAR},
      </if>
      <if test="successTime != null">
        success_time = #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmRefundReqId != null">
        confirm_refund_req_id = #{confirmRefundReqId},
      </if>
      <if test="feeAmount != null">
        fee_amount = #{feeAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.payment.model.po.Refund">
    update refund
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      after_sale_id = #{afterSaleId,jdbcType=BIGINT},
      refund_no = #{refundNo,jdbcType=VARCHAR},
      refund_status = #{refundStatus,jdbcType=INTEGER},
      sub_mchid = #{subMchid,jdbcType=VARCHAR},
      refund_price = #{refundPrice,jdbcType=DECIMAL},
      payment_price = #{paymentPrice,jdbcType=DECIMAL},
      refund_id = #{refundId,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      user_received_account = #{userReceivedAccount,jdbcType=VARCHAR},
      success_time = #{successTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByRefundNoSelective">
    update refund
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="afterSaleId != null">
        after_sale_id = #{afterSaleId,jdbcType=BIGINT},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="subMchid != null">
        sub_mchid = #{subMchid,jdbcType=VARCHAR},
      </if>
      <if test="refundPrice != null">
        refund_price = #{refundPrice,jdbcType=DECIMAL},
      </if>
      <if test="paymentPrice != null">
        payment_price = #{paymentPrice,jdbcType=DECIMAL},
      </if>
      <if test="refundId != null">
        refund_id = #{refundId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="userReceivedAccount != null">
        user_received_account = #{userReceivedAccount,jdbcType=VARCHAR},
      </if>
      <if test="successTime != null">
        success_time = #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reqSeqId != null">
        req_seq_id = #{reqSeqId,jdbcType=TIMESTAMP},
      </if>
    </set>
    where refund_no = #{refundNo, jdbcType=VARCHAR}
  </update>

  <update id="increaseRetryNumByIdAndStatus">
    update refund
    set retry_num = retry_num + 1
    where id = #{id,jdbcType=BIGINT} AND refund_status = #{status}
  </update>

  <update id="increaseRetryNumById">
    update refund
    set retry_num = retry_num + 1
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateStatusCas">
    update refund
    <set>
      refund_status = #{newStatus},
      <if test="newStatus == 100">
        retry_num = 0,
      </if>
      <if test="newStatus == 2">
        success_time = now(),
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT} AND refund_status = #{beforeStatus}
  </update>

  <update id="updateRefundReqIdCas" parameterType="com.cosfo.mall.payment.model.dto.HuifuRefundUpdateDTO">
    update refund
    set req_seq_id = #{newReqSeqId}
    where id = #{id,jdbcType=BIGINT}
    <if test="reqSeqId != null">
      AND req_seq_id = #{reqSeqId}
    </if>
    <if test="reqSeqId == null">
      AND req_seq_id IS NULL
    </if>
  </update>

  <select id="selectByRefundNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from refund
    where refund_no = #{refundNo}
  </select>

  <select id="selectByRefundId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from refund
    where refund_id = #{refundId}
  </select>

  <select id="selectByNeedRetryList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from refund
    where create_time between #{startTime} and #{endTime}
    AND refund_status IN
    <foreach collection="statusList" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    AND retry_num <![CDATA[ <= ]]> #{retryNum}
    order by create_time desc
    LIMIT #{limitSize}
  </select>

  <select id="selectByNeedRetryColdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from refund
    where create_time between #{startTime} and #{endTime}
    AND refund_status = #{status}
    AND retry_num <![CDATA[ > ]]> #{retryNum}
    AND retry_num <![CDATA[ < ]]> 12
    order by id desc
    LIMIT 50
  </select>

  <select id="selectByAfterSaleId" resultType="com.cosfo.mall.payment.model.po.Refund">
    select
    <include refid="Base_Column_List"/>
    from refund
    where after_sale_id = #{afterSaleId} and tenant_id = #{tenantId}
  </select>

  <select id="selectByIdForUpdate" resultType="com.cosfo.mall.payment.model.po.Refund">
    select
    <include refid="Base_Column_List"/>
    from refund
    where id = #{id} for update
  </select>

  <update id="updateConfirmRefundReqIdCas">
    update refund
    set confirm_refund_req_id = #{newConfirmRefundReqId}
    where id = #{id}
    <if test="oldConfirmRefundReqId != null">
      and confirm_refund_req_id = #{oldConfirmRefundReqId}
    </if>
    <if test="oldConfirmRefundReqId == null">
      and confirm_refund_req_id is null
    </if>
  </update>

  <insert id="batchInsert" parameterType="java.util.List">
    <!-- 批量插入语句 -->
    INSERT INTO refund (tenant_id, after_sale_id, refund_no, refund_status, sub_mchid, refund_price, payment_price,
    refund_id, channel, user_received_account, success_time, status, create_time, retry_num, req_seq_id,
    confirm_refund_req_id, payment_id)
    VALUES
    <foreach collection="list" item="refund" separator=",">
      (
      #{refund.tenantId},
      #{refund.afterSaleId},
      #{refund.refundNo},
      #{refund.refundStatus},
      #{refund.subMchid},
      #{refund.refundPrice},
      #{refund.paymentPrice},
      #{refund.refundId},
      #{refund.channel},
      #{refund.userReceivedAccount},
      #{refund.successTime},
      #{refund.status},
      #{refund.createTime},
      #{refund.retryNum},
      #{refund.reqSeqId},
      #{refund.confirmRefundReqId},
      #{refund.paymentId}
      )
    </foreach>
  </insert>

  <select id="querySuccessRefundsByTenant" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from refund
    <where>
      refund_status = 2
      and create_time between date_format(#{startTime}, '%Y-%m-%d 00:00:00') and date_format(#{endTime}, '%Y-%m-%d 23:59:59')
      and tenant_id in
      <foreach collection="tenantIds" item="id" close=")" open="(" separator=",">
      #{id}
    </foreach>
    </where>
  </select>
  <select id="querySuccessRefund" resultType="com.cosfo.mall.payment.model.po.Refund">
    select
    <include refid="Base_Column_List"/>
    from refund
    <where>
      refund_status = 2
      and create_time between date_format(#{startTime}, '%Y-%m-%d 00:00:00') and date_format(#{endTime}, '%Y-%m-%d 23:59:59')
    </where>
  </select>

  <select id="queryAfterSaleIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from refund
    where tenant_id = #{tenantId}
    and after_sale_id in
    <foreach collection="orderAfterSaleIds" item="id" close=")" open="(" separator=",">
      #{id}
    </foreach>
  </select>
</mapper>
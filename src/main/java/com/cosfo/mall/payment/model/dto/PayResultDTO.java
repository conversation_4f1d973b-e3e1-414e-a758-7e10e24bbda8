package com.cosfo.mall.payment.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @desc 支付结果 dto
 * <AUTHOR>
 * @date 2023/3/16 15:18
 */
@Data
public class PayResultDTO {

    /**
     * 时间戳
     */
    private String timeStamp;
    /**
     * 随机字符串
     */
    private String nonceStr;
    /**
     * 小程序下单接口返回的prepay_id参数值，提交格式如：prepay_id=***
     */
    @JsonProperty("package")
    private String packageStr;
    /**
     * 签名方式
     */
    private String signType;
    /**
     * 签名
     */
    private String paySign;

    /**
     * 公众号appId
     */
    private String oaAppId;
}

package com.cosfo.mall.payment.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/18
 */
@Data
public class RefundAcctSplitDetailDTO {
    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 分账接收方租户Id
     */
    private Long acctSplitTenantId;
    /**
     * 分账接收方的汇付Id
     * 【已废弃】转更通用的merchantNo
     */
    @Deprecated
    private String huifuId;

    /**
     * 分账接收方的商户号
     */
    private String merchantNo;
    /**
     * 退款金额
     */
    private BigDecimal divAmt;
}


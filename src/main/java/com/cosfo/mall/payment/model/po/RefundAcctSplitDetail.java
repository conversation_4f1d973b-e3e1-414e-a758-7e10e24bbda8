package com.cosfo.mall.payment.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/18
 */
@Data
@TableName("refund_acct_split_detail")
public class RefundAcctSplitDetail implements Serializable {
    /**
     * 主键id
     */
    @TableId("id")
    private Long id;

    /**
     * 分账租户Id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 分账金额
     */
    @TableField("div_amt")
    private BigDecimal divAmt;

    /**
     * 被分账方Id
     *【已废弃】转更通用的merchantNo
     */
    @TableField("huifu_id")
    private String huifuId;

    /**
     * 分账接收方的商户号
     */
    @TableField("merchant_no")
    private String merchantNo;

    /**
     * 退款Id
     */
    @TableField("refund_id")
    private Long refundId;

    /**
     * 被分账租户Id
     */
    @TableField("acct_split_tenant_id")
    private Long acctSplitTenantId;

    private static final long serialVersionUID = 1L;
}

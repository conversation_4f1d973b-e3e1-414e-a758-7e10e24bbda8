package com.cosfo.mall.payment.service.impl;

import com.cosfo.mall.payment.mapper.PaymentCombinedOrderDetailMapper;
import com.cosfo.mall.payment.model.po.PaymentCombinedOrderDetail;
import com.cosfo.mall.payment.service.PaymentCombinedOrderDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2025-04-30
 **/
@Service
public class PaymentCombinedOrderDetailServiceImpl implements PaymentCombinedOrderDetailService {

    @Resource
    private PaymentCombinedOrderDetailMapper paymentCombinedOrderDetailMapper;

    @Override
    public List<PaymentCombinedOrderDetail> selectByCombinedDetailIds(Long tenantId, List<Long> combinedDetailId) {
        return paymentCombinedOrderDetailMapper.selectByCombinedDetailIds(tenantId, combinedDetailId);
    }
}

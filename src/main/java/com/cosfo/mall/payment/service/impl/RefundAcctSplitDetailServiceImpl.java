package com.cosfo.mall.payment.service.impl;

import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.order.converter.RefundAcctSplitDetailConvert;
import com.cosfo.mall.payment.dao.RefundAcctSplitDetailDao;
import com.cosfo.mall.payment.model.dto.RefundAcctSplitDetailDTO;
import com.cosfo.mall.payment.model.po.RefundAcctSplitDetail;
import com.cosfo.mall.payment.service.RefundAcctSplitDetailService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/19
 */
@Service
public class RefundAcctSplitDetailServiceImpl implements RefundAcctSplitDetailService {

    @Resource
    private RefundAcctSplitDetailDao refundAcctSplitDetailDao;

    @Override
    public void save(List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOList, Long refundId) {
        if(CollectionUtils.isEmpty(refundAcctSplitDetailDTOList)){
            return;
        }

        List<RefundAcctSplitDetail> refundAcctSplitDetails = refundAcctSplitDetailDTOList.stream().map(refundAcctSplitDetailDTO -> {
            RefundAcctSplitDetail refundAcctSplitDetail = new RefundAcctSplitDetail();
            refundAcctSplitDetail.setTenantId(refundAcctSplitDetailDTO.getTenantId());
            refundAcctSplitDetail.setAcctSplitTenantId(refundAcctSplitDetailDTO.getAcctSplitTenantId());
            refundAcctSplitDetail.setHuifuId(refundAcctSplitDetailDTO.getHuifuId());
            refundAcctSplitDetail.setMerchantNo(refundAcctSplitDetailDTO.getMerchantNo());
            refundAcctSplitDetail.setDivAmt(refundAcctSplitDetailDTO.getDivAmt());
            refundAcctSplitDetail.setRefundId(refundId);
            return refundAcctSplitDetail;
        }).collect(Collectors.toList());

        refundAcctSplitDetailDao.saveBatch(refundAcctSplitDetails);
    }

    @Override
    public List<RefundAcctSplitDetailDTO> queryByRefundId(Long refundId, Long tenantId) {
        AssertCheckParams.notNull(refundId, ResultDTOEnum.REFUND_ID_PARARM_MISSING.getCode(), ResultDTOEnum.REFUND_ID_PARARM_MISSING.getMessage());
        List<RefundAcctSplitDetail> refundAcctSplitDetails = refundAcctSplitDetailDao.queryByRefundId(refundId, tenantId);
        if(CollectionUtils.isEmpty(refundAcctSplitDetails)){
            return Collections.emptyList();
        }

        List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOS = RefundAcctSplitDetailConvert.INSTANCE.toRefundAcctSplitDetailDTOList(refundAcctSplitDetails);
        return refundAcctSplitDetailDTOS;
    }
}

package com.cosfo.mall.payment.service;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.order.model.dto.HuiFuPaymentRefundResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinRefundNotifyDTO;
import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.wechat.bean.refund.RefundNotify;

import java.util.Collection;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/25  11:39
 */
public interface RefundService {
    /**
     * 发起退款
     *
     * @param refundDTO 退款信息
     * @return 请求退款结果
     */
    ResultDTO refundRequest(RefundDTO refundDTO);

    /**
     * 处理退款回调
     * @param result 回调信息
     * @return 退款回调
     */
    void handleWxRefundNotify(RefundNotify refundNotify);

    /**
     * 汇付退款回调处理
     * @param request 退款
     * @return
     */
    String handleHuiFuRefundNotify(HttpServletRequest request);

    /**
     * 汇付业务处理
     * @param refundResponse
     * @return
     */
    String huiFuRefundNotify(HuiFuPaymentRefundResponseDTO refundResponse);

    /**
     * 智付退款回调处理
     *
     * @param refundNotifyDTO
     * @return
     */
    String handleDinPayRefundNotify(DinRefundNotifyDTO refundNotifyDTO);

    /**
     * 交易确认退款
     *
     * @param refundDTO
     */
    void confirmRefund(RefundDTO refundDTO);


    /**
     * 执行退款
     *
     * @param refundId
     */
    void executeRefund(Long refundId);


    /**
     * 批量保存
     *
     * @param refunds
     */
    int batchInsert(List<Refund> refunds);

    List<Refund> querySuccessRefunds(Collection<Long> tenantIds, String startTime, String endTime);

    /**
     * 根据售后单id查询
     *
     * @param tenantId
     * @param orderAfterSaleIds
     * @return
     */
    List<Refund> queryAfterSaleIds(Long tenantId, List<Long> orderAfterSaleIds);

    void logRefundResult(Refund refund, Exception e, Integer warningNum);

    /**
     * 根据id查询退款单
     *
     * @param id
     * @return
     */
    Refund selectByPrimaryKey(Long id);
}

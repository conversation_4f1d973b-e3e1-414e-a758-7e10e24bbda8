package com.cosfo.mall.payment.controller;

import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.service.RefundService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/25
 */
@RestController
@Slf4j
public class RefundController {
    @Resource
    private RefundService refundService;

    /**
     * 退款请求
     *
     * @param refundDTO
     */
    @RequestMapping(value = "/pay/refund", method = RequestMethod.POST)
    public void refund(@RequestBody RefundDTO refundDTO) {
        refundService.refundRequest(refundDTO);
    }

    /**
     * 执行退款
     *
     * @param refundDTO
     */
    @RequestMapping(value = "/pay/execute-refund", method = RequestMethod.POST)
    public void executeRefund(@RequestBody RefundDTO refundDTO) {
        try {
            refundService.executeRefund(refundDTO.getId());
        } catch (Exception e) {
            log.info("退款单：{}，退款失败", refundDTO.getId());
        }
    }
}

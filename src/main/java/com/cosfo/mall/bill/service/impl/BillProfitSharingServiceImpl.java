package com.cosfo.mall.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.bill.convert.BillProfitSharingConvert;
import com.cosfo.mall.bill.convert.BillProfitSharingOrderConvert;
import com.cosfo.mall.bill.convert.BillProfitSharingSnapshotConvert;
import com.cosfo.mall.bill.mapper.BillProfitSharingMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingOrderMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingRuleMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingSnapshotMapper;
import com.cosfo.mall.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharing;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.model.po.BillProfitSharingRefundSnapshot;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.bill.repository.BillProfitSharingRefundSnapshotRepository;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.OnlinePayChannelEnum;
import com.cosfo.mall.common.constants.ProfitSharingBusinessType;
import com.cosfo.mall.common.constants.ProfitSharingResultEnum;
import com.cosfo.mall.common.context.BillProfitSharingOrderStatusEnum;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.common.context.shard.AccountTypeEnum;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.dto.HuiFuConfirmRefundResultDTO;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.model.dto.RefundAcctSplitDetailDTO;
import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.mall.wechat.bean.profitsharing.QueryOrderParams;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Slf4j
@Service
public class BillProfitSharingServiceImpl implements BillProfitSharingService {
    @Resource
    private BillProfitSharingRuleMapper billProfitSharingRuleMapper;
    @Resource
    private BillProfitSharingSnapshotMapper billProfitSharingSnapshotMapper;
    @Resource
    private BillProfitSharingOrderMapper billProfitSharingOrderMapper;

    @Resource
    private BillProfitSharingMapper billProfitSharingMapper;
    @Lazy
    @Resource
    private PaymentService paymentService;
    @Resource
    private TenantService tenantService;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private BillProfitSharingRefundSnapshotRepository billProfitSharingRefundSnapshotRepository;
    @Resource
    private BillProfitSharingService billProfitSharingService;


    @Override
    public void queryProfitSharingResult(String startTime, String endTime) {
        if (startTime != null && endTime != null) {
            log.info("开始查询分账结果，时间范围：{} - {}", startTime, endTime);
        } else {
            endTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            startTime = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        // 查询分账处理中订单
        List<BillProfitSharingOrder> billProfitSharingOrders = billProfitSharingOrderMapper.queryProfitSharingOrderForProcessIng(startTime, endTime);
        if (CollectionUtils.isEmpty(billProfitSharingOrders)) {
            log.info("未查询到需要处理的分账订单");
            return;
        }

        log.info("查询到{}个需要处理的分账订单", billProfitSharingOrders.size());

        billProfitSharingOrders.stream().forEach(billProfitSharingOrder -> {
            try {
                dealProfitSharingResultQuery(billProfitSharingOrder);
            } catch (Exception exception) {
                log.error("查询分账结果失败，订单ID：{}，分账单号：{}",
                        billProfitSharingOrder.getOrderId(),
                        billProfitSharingOrder.getProfitSharingNo(), exception);
            }
        });

        log.info("分账结果查询完成，处理订单数：{}", billProfitSharingOrders.size());
    }

    @Override
    public void updateByPrimaryKeySelective(BillProfitSharing billProfitSharing) {
        billProfitSharingMapper.updateByPrimaryKeySelective(billProfitSharing);
    }

    @Override
    public void batchUpdateBillProFitSharing(BillProfitSharing update, List<Long> sharingIds) {
        if (CollectionUtils.isEmpty(sharingIds)) {
            return;
        }
        billProfitSharingMapper.batchUpdateBillProFitSharing(update, sharingIds);
    }

    @Override
    public List<BillProfitSharingSnapshotDTO> queryOrderProfitSharingRuleSnapshot(Long tenantId, Long orderId) {
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotMapper.queryByTenantIdAndOrderId(tenantId, orderId);
        List<BillProfitSharingSnapshotDTO> list = billProfitSharingSnapshots.stream().map(BillProfitSharingSnapshot -> {
            BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO = new BillProfitSharingSnapshotDTO();
            BeanUtils.copyProperties(BillProfitSharingSnapshot, billProfitSharingSnapshotDTO);
            return billProfitSharingSnapshotDTO;
        }).collect(Collectors.toList());
        return list;
    }

    @Override
    public void updateBillProfitSharingSnapshot(BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO) {
        BillProfitSharingSnapshot billProfitSharingSnapshot = new BillProfitSharingSnapshot();
        BeanUtils.copyProperties(billProfitSharingSnapshotDTO, billProfitSharingSnapshot);
        billProfitSharingSnapshotMapper.updateByPrimaryKeySelective(billProfitSharingSnapshot);
    }

    @Override
    public void saveBillProfitSharing(BillProfitSharing billProfitSharing) {
        billProfitSharingMapper.insertSelective(billProfitSharing);
    }

    /**
     * 查询分账结果查询
     *
     * @param billProfitSharingOrder
     */
    private void dealProfitSharingResultQuery(BillProfitSharingOrder billProfitSharingOrder) {
        List<BillProfitSharing> billProfitSharings = billProfitSharingMapper.queryByTenantIdAndOutTradeNo(billProfitSharingOrder.getTenantId(), billProfitSharingOrder.getProfitSharingNo(), ProfitSharingBusinessType.FORWARD_DIRECTION.getCode());
        List<BillProfitSharing> processingList = billProfitSharings.stream()
                .filter(el -> Objects.equals(el.getStatus(), ProfitSharingResultEnum.PROCESSING.getStatus()))
                .collect(Collectors.toList());
        // 查询分账记录
        if (CollectionUtils.isEmpty(processingList)) {
            log.error("分账单号:{}无处理中的分账记录，跳过查询", billProfitSharingOrder.getProfitSharingNo());
            return;
        }
        // 查询支付记录
        PaymentDTO paymentDTO = paymentService.querySuccessPaymentInfoByOrderId(billProfitSharingOrder.getOrderId(), billProfitSharingOrder.getTenantId());
        Integer profitSharingChannel = processingList.get(0).getProfitSharingChannel();
        if (OnlinePayChannelEnum.HUIFU_PAY.getChannel().equals(profitSharingChannel)) {
            // 处理汇付分账结果查询
            HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, paymentDTO.getId()));

            BillProfitSharing billProfitSharing = billProfitSharings.get(NumberConstant.ZERO);
            QueryOrderParams queryOrderParams = new QueryOrderParams();
            queryOrderParams.setOutOrderNo(billProfitSharing.getOutTradeNo());
            queryOrderParams.setOrgReqDate(billProfitSharing.getCreateTime().format(DateTimeFormatter.BASIC_ISO_DATE));
            queryOrderParams.setHuiFuId(huiFuPayment.getHuifuId());
            queryOrderParams.setOrderId(billProfitSharing.getOrderId());
            // 查询汇付分账结果
            BillProfitSharingOrderDTO billProfitSharingOrderDTO = BillProfitSharingOrderConvert.toBillProfitSharingOrderDTO(billProfitSharingOrder);
            paymentService.handleHuiFuProfitSharingOrderResult(queryOrderParams, billProfitSharings, billProfitSharingOrderDTO);
        } else if (OnlinePayChannelEnum.DIN_PAY.getChannel().equals(profitSharingChannel)) {
            // 处理智付分账结果查询
            handleDinPayProfitSharingQuery(billProfitSharingOrder, processingList, paymentDTO);
        }

    }

    /**
     * 处理智付分账结果查询
     */
    private void handleDinPayProfitSharingQuery(BillProfitSharingOrder billProfitSharingOrder,
                                                List<BillProfitSharing> billProfitList,
                                                PaymentDTO paymentDTO) {
        try {
            log.info("开始定时查询智付分账状态 - 订单ID：{}，分账单号：{}",
                    billProfitSharingOrder.getOrderId(), billProfitSharingOrder.getProfitSharingNo());

            // 2. 调用智付分账查询接口
            BillProfitSharingOrderDTO billProfitSharingOrderDTO = BillProfitSharingOrderConvert.toBillProfitSharingOrderDTO(billProfitSharingOrder);
            paymentService.queryAndUpdateDinPayProfitSharingStatus(billProfitList, paymentDTO, billProfitSharingOrderDTO);

            log.info("智付分账状态定时查询完成 - 订单ID：{}", billProfitSharingOrder.getOrderId());

        } catch (Exception e) {
            log.error("智付分账状态定时查询失败 - 订单ID：{}，分账单号：{}",
                    billProfitSharingOrder.getOrderId(), billProfitSharingOrder.getProfitSharingNo(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<BillProfitSharingDTO> createConfirmRefundRecord(RefundDTO refundDTO) {
        // 查询订单交易确认流水
        List<BillProfitSharing> billProfitSharings = billProfitSharingMapper.querySuccessByTenantIdAndOrderId(refundDTO.getTenantId(), refundDTO.getOrderId(), ProfitSharingBusinessType.FORWARD_DIRECTION.getCode());
        if(CollectionUtils.isEmpty(billProfitSharings)){
            throw new BizException("该订单：" + refundDTO.getOrderId() + "未进行分账");
        }

        // 交易确认请求流水号
        Long afterSaleId = refundDTO.getAfterSaleId();
        List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = billProfitSharingRefundSnapshotRepository.queryByAfterSaleId(refundDTO.getTenantId(), afterSaleId);
        if (CollectionUtils.isEmpty(billProfitSharingRefundSnapshots)) {
            throw new BizException("未查询到交易确认退款记录，售后单：" + afterSaleId);
        }

        String orgProfitSharingNo = billProfitSharingRefundSnapshots.get(0).getOrgProfitSharingNo();
        String orgReqSeqId = null;
        String orgReqDate = null;
        BillProfitSharing orgBillProfitSharing = null;
        if (Objects.nonNull(orgProfitSharingNo)) {
            orgBillProfitSharing = billProfitSharings.stream().filter(el -> Objects.equals(el.getOutTradeNo(), orgProfitSharingNo)).findFirst().orElseThrow(() -> new BizException("未查询到交易确认退款记录，售后单：" + afterSaleId));
        } else {
            orgBillProfitSharing = billProfitSharings.get(NumberConstant.ZERO);
        }
        orgReqSeqId = orgBillProfitSharing.getOutTradeNo();
        orgReqDate = TimeUtils.changeDate2String(orgBillProfitSharing.getSuccessTime(), TimeUtils.FORMAT_STRING);
        List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOList = refundDTO.getRefundAcctSplitDetailDTOList();
        // 创建交易确认退款记录
        String confirmRefundNo = Global.generateConfirmRefundNo(refundDTO.getAfterSaleId());
        String finalOrgReqSeqId = orgReqSeqId;

        // 分账渠道
        Integer profitSharingChannel = billProfitSharings.get(0).getProfitSharingChannel();

        Map<Long, Integer> accountTypeMap = billProfitSharings.stream().filter(el -> Objects.nonNull(el.getAccountType())).collect(Collectors.toMap(BillProfitSharing::getReceiverTenantId, BillProfitSharing::getAccountType, (k1, k2) -> k1));
        List<BillProfitSharing> billProfitSharingList = refundAcctSplitDetailDTOList.stream().map(refundAcctSplitDetailDTO -> {
            BillProfitSharing billProfitSharing = new BillProfitSharing();
            billProfitSharing.setTenantId(refundDTO.getTenantId());
            billProfitSharing.setReceiverTenantId(refundAcctSplitDetailDTO.getAcctSplitTenantId());
            billProfitSharing.setOrderId(refundDTO.getOrderId());
            billProfitSharing.setType("MERCHANT_ID");
            // HuiFuId被弃用掉了，转通用的merchantNo
            String account = Optional.ofNullable(refundAcctSplitDetailDTO.getMerchantNo()).orElse(refundAcctSplitDetailDTO.getHuifuId());
            billProfitSharing.setAccount(account);
            billProfitSharing.setBusinessType(ProfitSharingBusinessType.REVERSE.getCode());
            // 请求流水号
            billProfitSharing.setOutTradeNo(confirmRefundNo);
            // 原交易请求流水号
            billProfitSharing.setTransactionId(finalOrgReqSeqId);
            // 交易确认退款金额
            billProfitSharing.setPrice(refundAcctSplitDetailDTO.getDivAmt().negate());
            billProfitSharing.setStatus(ProfitSharingResultEnum.WAITING.getStatus());
            billProfitSharing.setAfterSaleId(refundDTO.getAfterSaleId());
            billProfitSharing.setAccountType(accountTypeMap.getOrDefault(refundAcctSplitDetailDTO.getAcctSplitTenantId(), AccountTypeEnum.getByTenantId(refundAcctSplitDetailDTO.getAcctSplitTenantId())));
            billProfitSharing.setProfitSharingChannel(profitSharingChannel);
            return billProfitSharing;
        }).collect(Collectors.toList());
        billProfitSharingMapper.saveBatch(billProfitSharingList);

        // 如果是智付 并且全分给自己 则直接记录成功明细
        boolean dinChannel = Objects.equals(profitSharingChannel, OnlinePayChannelEnum.DIN_PAY.getChannel());
        boolean allSelf = billProfitSharingList.stream()
                .allMatch(el -> Objects.equals(AccountTypeEnum.TENANT.getType(), el.getAccountType()));
        if (dinChannel && allSelf) {
            billProfitSharingList.forEach(el -> {
                el.setStatus(ProfitSharingResultEnum.FINISHED.getStatus());
                el.setSuccessTime(new Date());
                billProfitSharingService.updateByPrimaryKeySelective(el);
            });
            int result = refundMapper.updateStatusCas(refundDTO.getId(), RefundEnum.Status.IN_CONFIRM_REFUND.getStatus(), RefundEnum.Status.CREATE_REFUND.getStatus());
            if (result <= 0) {
                throw new BizException("分账回退成功，退款任务状态更新失败");
            }
            log.info("分账单号:{}，智付渠道且全部是自己分账回退，直接记录分账回退成功明细", refundDTO.getConfirmRefundReqId());
            return Collections.emptyList();
        }

        // 乐观更新为交易确认退款请求Id
        int result = refundMapper.updateConfirmRefundReqIdCas(refundDTO.getId(), refundDTO.getConfirmRefundReqId(), confirmRefundNo);
        if (result <= 0) {
            throw new BizException("创建交易确认退款流水失败，售后单：" + refundDTO.getAfterSaleId());
        }

        List<BillProfitSharingDTO> billProfitSharingDTOS = BillProfitSharingConvert.INSTANCE.toBillProfitSharingDTO(billProfitSharingList);
        String finalOrgReqDate = orgReqDate;
        billProfitSharingDTOS.stream().forEach(billProfitSharingDTO -> billProfitSharingDTO.setOrgReqDate(finalOrgReqDate));
        return billProfitSharingDTOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean handleConfirmRefundRecordResult(HuiFuConfirmRefundResultDTO huiFuConfirmRefundResultDTO, List<BillProfitSharingDTO> billProfitSharingDTOList, Long refundId) {
        ProfitSharingResultEnum status = ProfitSharingResultEnum.getByHuiFuCode(huiFuConfirmRefundResultDTO.getTransStat());
        List<Long> profitSharingIds = billProfitSharingDTOList.stream().map(BillProfitSharingDTO::getId).collect(Collectors.toList());
        BillProfitSharing billProfitSharing = new BillProfitSharing();
        billProfitSharing.setStatus(status.getStatus());
        billProfitSharing.setWxOrderId(huiFuConfirmRefundResultDTO.getHfSeqId());
        billProfitSharing.setSuccessTime(new Date());
        billProfitSharing.setDetailId(huiFuConfirmRefundResultDTO.getRespCode());
        billProfitSharing.setFailReason(huiFuConfirmRefundResultDTO.getRespDesc());
        // 更新 bill_profit_sharing表
        billProfitSharingMapper.batchUpdateBillProFitSharing(billProfitSharing,profitSharingIds);
        // 如果成功 更新refund status为100, 次数变为0
        Integer refundStatus = null;
        refundStatus = ProfitSharingResultEnum.FINISHED.equals(status) ? RefundEnum.Status.CREATE_REFUND.getStatus() : ProfitSharingResultEnum.FAILED.equals(status) ? RefundEnum.Status.CONFIRM_REFUND.getStatus() : null;
        if(Objects.nonNull(refundStatus)){
            int size = refundMapper.updateStatusCas(refundId, RefundEnum.Status.IN_CONFIRM_REFUND.getStatus(), refundStatus);
            return size > NumberConstant.ZERO;
        }

        return true;
    }

    @Override
    public List<BillProfitSharingDTO> queryConfirmResultRecord(Long tenantId, String confirmRefundReqId) {
        List<BillProfitSharing> billProfitSharings = billProfitSharingMapper.queryByTenantIdAndOutTradeNo(tenantId, confirmRefundReqId, ProfitSharingBusinessType.REVERSE.getCode());
        if(CollectionUtils.isEmpty(billProfitSharings)){
            return Collections.emptyList();
        }

        return BillProfitSharingConvert.INSTANCE.toBillProfitSharingDTO(billProfitSharings);
    }

    @Override
    public List<BillProfitSharingSnapshotDTO> queryByProfitSharingNo(Long tenantId, String profitSharingNo) {
        List<BillProfitSharingSnapshot> billProfitSharingSnapshotDTOS = billProfitSharingSnapshotMapper.queryByProfitSharingNo(tenantId, profitSharingNo);
        return BillProfitSharingSnapshotConvert.toDTOList(billProfitSharingSnapshotDTOS);
    }

    @Override
    public void batchSaveBillProfitSharing(List<BillProfitSharing> billProfitSharingList) {
        billProfitSharingMapper.saveBatch(billProfitSharingList);
    }
}



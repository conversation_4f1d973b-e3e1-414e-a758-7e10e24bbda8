package com.cosfo.mall.bill.service.impl;

import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.mall.bill.convert.BillProfitSharingOrderConvert;
import com.cosfo.mall.bill.mapper.BillProfitSharingOrderMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingSnapshotMapper;
import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import com.cosfo.mall.common.context.shard.ProfitSharingDeliveryTypeEnums;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderItemSnapshotQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/24
 */
@Service
@Slf4j
public class BillProfitSharingOrderServiceImpl implements BillProfitSharingOrderService {
    @Resource
    private BillProfitSharingOrderMapper billProfitSharingOrderMapper;
    @DubboReference
    private OrderItemSnapshotQueryProvider orderItemSnapshotQueryProvider;
    @Resource
    private OrderService orderService;
    @Resource
    private BillProfitSharingSnapshotMapper billProfitSharingSnapshotMapper;

    @Override
    public List<BillProfitSharingOrderDTO> queryWaitingProfitSharingOrder(Integer maxRetryNum) {
        // 查询等待分账订单
        List<BillProfitSharingOrder> billProfitSharingOrders = billProfitSharingOrderMapper.queryWaitingProfitSharingOrder(maxRetryNum);
        if(CollectionUtils.isEmpty(billProfitSharingOrders)){
            return new ArrayList<>();
        }

        List<BillProfitSharingOrderDTO> billProfitSharingOrderDtos = billProfitSharingOrders.stream().map(billProfitSharingOrder -> {
            BillProfitSharingOrderDTO billProfitSharingOrderDto = new BillProfitSharingOrderDTO();
            BeanUtils.copyProperties(billProfitSharingOrder, billProfitSharingOrderDto);
            return billProfitSharingOrderDto;
        }).collect(Collectors.toList());
        return billProfitSharingOrderDtos;
    }


    @Override
    public int updateBillProfitSharingOrderStatus(Long tenantId, Long orderId, Integer status, Integer originStatus) {
        int i = billProfitSharingOrderMapper.updateStatusByOrderId(tenantId, orderId, status, originStatus);
        return i;
    }

    @Override
    public void save(BillProfitSharingOrder billProfitSharingOrder) {
        billProfitSharingOrderMapper.insertSelective(billProfitSharingOrder);
    }

    @Override
    public List<BillProfitSharingOrderDTO> queryByOrderIds(List<Long> orderIds, Long tenantId) {
        if(CollectionUtils.isEmpty(orderIds)){
            return Collections.emptyList();
        }

        List<BillProfitSharingOrder> billProfitSharingOrder = billProfitSharingOrderMapper.queryByOrderIdsAndTenantId(tenantId, orderIds);
        if(CollectionUtils.isEmpty(billProfitSharingOrder)){
            return Collections.emptyList();
        }

        return BillProfitSharingOrderConvert.convertToList(billProfitSharingOrder);
    }

    @Override
    public int resetStatusAndIncRetryNum(Long id) {
        return billProfitSharingOrderMapper.resetStatusAndIncRetryNum(id);
    }

    @Override
    public BillProfitSharingOrder queryById(Long id) {
        return billProfitSharingOrderMapper.selectByPrimaryKey(id);
    }

    @Override
    public BillProfitSharingOrder queryByProfitSharingNo(String profitSharingNo) {
        return billProfitSharingOrderMapper.selectByProfitSharingNo(profitSharingNo);
    }

    @Override
    public int updateStatusById(Long id, Integer finalStatus, Integer orgStatus) {
        return billProfitSharingOrderMapper.updateStatusById(id, finalStatus, orgStatus);
    }

    @Override
    public BillProfitSharingOrder getBillProfitSharingOrder(Long tenantId, Long orderId, Long orderItemId) {
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotMapper.queryByTenantIdAndOrderId(tenantId, orderId);
        if (CollectionUtils.isEmpty(billProfitSharingSnapshots)) {
            return null;
        }
        BillProfitSharingSnapshot snapshot = billProfitSharingSnapshots.get(0);
        Integer deliveryType = snapshot.getDeliveryType();
        Long supplierId = null;
        if (Objects.equals(deliveryType, ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType())) {
            OrderItemSnapshotResp orderItemSnapshotDTO = RpcResultUtil.handle(orderItemSnapshotQueryProvider.queryByOrderItemId(orderItemId));
            if (Objects.isNull(orderItemSnapshotDTO)) {
                throw new ProviderException("未查询到订单项快照信息");
            }
            Integer goodsType = orderItemSnapshotDTO.getGoodsType();
            supplierId = Objects.equals(goodsType, GoodsTypeEnum.NO_GOOD_TYPE.getCode()) ? orderItemSnapshotDTO.getSupplierTenantId() : null;
        }
        return billProfitSharingOrderMapper.queryByTenantAndOrderAndSupplierId(tenantId, orderId, supplierId);
    }

    @Override
    public void confirmProfitSharingTask() {
        List<Long> orderIds = billProfitSharingOrderMapper.queryInitStatusOrderIds();
        if (CollectionUtils.isEmpty(orderIds)) {
            log.info("未查询到需要检查确认的订单");
            return;
        }
        for (Long orderId : orderIds) {
            orderService.doConfirmProfitSharing(orderId);
        }
    }

    @Override
    public List<BillProfitSharingOrder> queryByTenantAndOrderId(Long tenantId, Long orderId) {
        return billProfitSharingOrderMapper.queryByOrderIdAndTenantId(tenantId, orderId);
    }

    @Override
    public int updateProfitSharingNo(Long id, String profitSharingNo) {
        return billProfitSharingOrderMapper.updateProfitSharingNo(id, profitSharingNo);
    }
}

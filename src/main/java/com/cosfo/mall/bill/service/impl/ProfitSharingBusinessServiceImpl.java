package com.cosfo.mall.bill.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.mall.bill.builder.ProfitSharingSnapshotBuilder;
import com.cosfo.mall.bill.builder.impl.*;
import com.cosfo.mall.bill.convert.BillProfitSharingOrderConvert;
import com.cosfo.mall.bill.convert.BillProfitSharingSnapshotConvert;
import com.cosfo.mall.bill.mapper.BillProfitSharingMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingRuleMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingSnapshotMapper;
import com.cosfo.mall.bill.model.dto.BillProfitSharingMessageDTO;
import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.model.po.*;
import com.cosfo.mall.bill.service.BillProfitSharingConfigService;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import com.cosfo.mall.common.config.FanTaiPaymentConfig;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.config.TenantGrayConfig;
import com.cosfo.mall.common.constant.MQTopicConstant;
import com.cosfo.mall.common.constant.MqTagConstant;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.PayTypeEnum;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.common.constants.*;
import com.cosfo.mall.common.context.*;
import com.cosfo.mall.common.context.DeliveryTypeEnum;
import com.cosfo.mall.common.context.shard.AccountTypeEnum;
import com.cosfo.mall.common.context.shard.ProfitSharingDeliveryTypeEnums;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.facade.payment.PaymentChannelFacade;
import com.cosfo.mall.huifu.model.po.HuiFuAccount;
import com.cosfo.mall.huifu.service.HuiFuAccountService;
import com.cosfo.mall.order.factory.OrderProfitSharingCalculateFactory;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.mapper.OrderAgentSkuFeeRuleSnapshotMapper;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.order.model.po.OrderAgentSkuFeeRuleSnapshot;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.order.service.OrderAgentSkuFeeRuleService;
import com.cosfo.mall.order.service.ProfitSharingCalculate;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.service.HuifuPaymentService;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeRuleDetailDTO;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.i18n.exception.I18nProviderException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ZERO;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProfitSharingBusinessServiceImpl implements ProfitSharingBusinessService {
    @Resource
    private TenantService tenantService;
    ;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Lazy
    @Resource
    private PaymentService paymentService;
    @Resource
    private BillProfitSharingService billProfitSharingService;
    @Resource
    private OrderAgentSkuFeeRuleService orderAgentSkuFeeRuleService;
    @Resource
    private OrderAgentSkuFeeRuleSnapshotMapper orderAgentSkuFeeRuleSnapshotMapper;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;

    @Resource
    private FanTaiPaymentConfig fanTaiPaymentConfig;
    @Resource
    private HuiFuConfig huiFuConfig;
    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;
    @Autowired
    private MqProducer mqProducer;
    @Resource
    private BillProfitSharingRuleMapper billProfitSharingRuleMapper;

    @Resource
    private OrderAfterSaleService orderAfterSaleBusinessService;
    @Resource
    private BillProfitSharingMapper billProfitSharingMapper;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private BillProfitSharingSnapshotMapper billProfitSharingSnapshotMapper;
    @Resource
    private HuiFuAccountService huiFuAccountService;
    @Resource
    private HuifuPaymentService huifuPaymentService;


    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @Resource
    private TenantGrayConfig tenantGrayConfig;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;
    @Resource
    private BillProfitSharingConfigService billProfitSharingConfigService;
    @Resource
    private PaymentChannelFacade paymentChannelFacade;


    @Override
    public List<OrderAgentSkuFeeRuleSnapshot> handleAgentSkuProfitSharing(Long orderId, Long tenantId) {
        // 查询代仓收费规则快照
        List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots = orderAgentSkuFeeRuleSnapshotMapper
                .queryByTenantIdAndOrderId(tenantId, orderId);
        if (CollectionUtils.isEmpty(orderAgentSkuFeeRuleSnapshots)) {
            return new ArrayList<>();
        }

        // 查询订单信息
//        Order order = orderMapper.selectByPrimaryKey(orderId);
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        // 查询订单项信息
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotList = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));
//        List<OrderItemVO> orderItems = orderItemMapper.queryOrderItemVOByOrderId(order.getTenantId(), order.getId());
        // 查询已经完成的售后单
        List<OrderAfterSaleResp> orderAfterSaleDtos = orderAfterSaleBusinessService.queryAllFinishedAfterSaleOrderByOrderId(orderDTO.getTenantId(), orderDTO.getId());
        // 计算商品价格
        Map<Long, List<OrderAfterSaleResp>> map = orderAfterSaleDtos.stream()
                .collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId));

        BigDecimal productTotalPrice = BigDecimal.ZERO;
        Integer agentSkuTotalAmount = NumberConstant.ZERO;
        for (OrderItemAndSnapshotResp orderItem : orderItemAndSnapshotList) {
            // 代仓商品
            if (OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTO.getWarehouseType())
                    && GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(orderItem.getGoodsType())) {
                productTotalPrice = NumberUtil.add(productTotalPrice, orderItem.getTotalPrice());
                BigDecimal refundTotalPrice = BigDecimal.ZERO;
                Integer refundAmount = NumberConstant.ZERO;
                if (map.containsKey(orderItem.getOrderItemId())) {
                    List<OrderAfterSaleResp> orderAfterSales = map.get(orderItem.getOrderItemId());
                    for (OrderAfterSaleResp orderAfterSaleDto : orderAfterSales) {
                        BigDecimal refundPrice = NumberUtil.sub(orderAfterSaleDto.getTotalPrice(),
                                Objects.isNull(orderAfterSaleDto.getDeliveryFee()) ? BigDecimal.ZERO : orderAfterSaleDto.getDeliveryFee());
                        refundTotalPrice = NumberUtil.add(refundPrice, refundTotalPrice);
                        // 按件数 需要根据责任人划分 客户责任的，需要计入分账件数 鲜沐责任的不需要计入分账件数
                        if (ResponsibilityTypeEnum.SUPPLIER.getType()
                                .equals(orderAfterSaleDto.getResponsibilityType()) || OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(orderAfterSaleDto.getAfterSaleType())) {
                            refundAmount = refundAmount + orderAfterSaleDto.getAmount();
                        }
                    }
                }

                productTotalPrice = NumberUtil.sub(productTotalPrice, refundTotalPrice);
                agentSkuTotalAmount = agentSkuTotalAmount + orderItem.getAmount();
                agentSkuTotalAmount = agentSkuTotalAmount - refundAmount;
            }
        }

        String rule = orderAgentSkuFeeRuleSnapshots.get(NumberConstant.ZERO).getRule();
        List<ProductAgentSkuFeeRuleDetailDTO> productAgentSkuFeeRuleDetailDTOS = JSONObject.parseArray(rule,
                ProductAgentSkuFeeRuleDetailDTO.class);
        if (ProductAgentSkuFeeRuleTypeEnum.ACCOUNT.getCode()
                .equals(orderAgentSkuFeeRuleSnapshots.get(NumberConstant.ZERO).getFeeRuleType())) {
            // 按件数处理代仓分账
            updateAgentSkuProfitSharingPriceByAccount(productAgentSkuFeeRuleDetailDTOS, orderAgentSkuFeeRuleSnapshots,
                    agentSkuTotalAmount, productTotalPrice);
        } else if (ProductAgentSkuFeeRuleTypeEnum.SELF_RATIO.getCode()
                .equals(orderAgentSkuFeeRuleSnapshots.get(NumberConstant.ZERO).getFeeRuleType())) {
            // 按比例处理代仓分账
            updateAgentSkuProfitSharingPriceByRate(productAgentSkuFeeRuleDetailDTOS, orderAgentSkuFeeRuleSnapshots,
                    productTotalPrice);
        }

        return orderAgentSkuFeeRuleSnapshots;
    }

    @Override
    public void doProfitSharing(String profitSharingNo) {
        BillProfitSharingOrder billProfitSharingOrder = billProfitSharingOrderService.queryByProfitSharingNo(profitSharingNo);
        if (Objects.isNull(billProfitSharingOrder)) {
            log.error("未查询到分账单号{}，结束分账", profitSharingNo, new ProviderException("未查询到分账信息"));
            return;
        }
        Long orderId = billProfitSharingOrder.getOrderId();
        OrderResp order = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        Long tenantId = order.getTenantId();

        // 判断订单有没有实际分账金额计算完成，如果计算完成，才开始分账
        BillProfitSharingOrderDTO billProfitSharingOrderDTO = BillProfitSharingOrderConvert.toBillProfitSharingOrderDTO(billProfitSharingOrder);
        if (!BillProfitSharingOrderStatusEnum.CALCULATE_FINISHED.getStatus().equals(billProfitSharingOrderDTO.getStatus())) {
            log.info("订单:{}分账金额未计算完成，结束分账", orderId);
            return;
        }

        // 更新订单分账状态在分账中
        int i = billProfitSharingOrderService.updateStatusById(billProfitSharingOrder.getId(), BillProfitSharingOrderStatusEnum.PROCESSING.getStatus(),
                BillProfitSharingOrderStatusEnum.CALCULATE_FINISHED.getStatus());
        if (i != NumberConstant.ONE) {
            log.error("订单:{}分账状态更新失败", orderId);
            throw new ProviderException("分账状态更新失败");
        }

        // 查询支付记录
        PaymentDTO paymentDTO = paymentService.querySuccessPaymentInfoByOrderId(orderId, tenantId);
        HuiFuPayment huiFuPayment = huifuPaymentService.selectByPaymentId(paymentDTO.getId());

        // 账户明细map
        Map<String, BigDecimal> accountDimensionProfitSharingMap = getProfitSharingAccountDimension(tenantId, orderId, profitSharingNo);

        Integer onlinePayChannel = paymentDTO.getOnlinePayChannel();
        List<BillProfitSharing> billProfitSharings = buildBillProfitSharingV2(tenantId, orderId, profitSharingNo, accountDimensionProfitSharingMap, paymentDTO);
        if (CollectionUtils.isEmpty(billProfitSharings)) {
            log.info("订单:{}本次无需分账", orderId);
            billProfitSharingOrderService.updateStatusById(billProfitSharingOrder.getId(), BillProfitSharingOrderStatusEnum.FINISHED.getStatus(), BillProfitSharingOrderStatusEnum.PROCESSING.getStatus());
            return;
        }
        billProfitSharingService.batchSaveBillProfitSharing(billProfitSharings);

        // 如果是智付 并且全分给自己 则直接记录成功明细
        boolean dinChannel = Objects.equals(onlinePayChannel, OnlinePayChannelEnum.DIN_PAY.getChannel());
        boolean allSelf = billProfitSharings.stream()
                .allMatch(el -> Objects.equals(AccountTypeEnum.TENANT.getType(), el.getAccountType()));
        if (dinChannel && allSelf) {
            billProfitSharings.forEach(el -> {
                el.setStatus(ProfitSharingResultEnum.FINISHED.getStatus());
                el.setSuccessTime(new Date());
                billProfitSharingService.updateByPrimaryKeySelective(el);
            });
            billProfitSharingOrderService.updateStatusById(billProfitSharingOrder.getId(), BillProfitSharingOrderStatusEnum.FINISHED.getStatus(), BillProfitSharingOrderStatusEnum.PROCESSING.getStatus());
            log.info("分账单号:{}，智付渠道且全部分账给自己，直接记录分账成功明细", profitSharingNo);
            return;
        }

        // 根据渠道调用分账接口
        if (Objects.equals(onlinePayChannel, OnlinePayChannelEnum.HUIFU_PAY.getChannel())) {
            // 汇付分账逻辑
            log.info("订单：[{}]执行汇付分账，分账单号：{}", orderId, profitSharingNo);
            paymentService.huifuProfitSharing(billProfitSharings, huiFuPayment, billProfitSharingOrderDTO);
        } else if (dinChannel) {
            // 智付分账逻辑
            log.info("订单：[{}]执行智付分账，分账单号：{}", orderId, profitSharingNo);
            paymentService.dinPayProfitSharing(billProfitSharings, paymentDTO, billProfitSharingOrderDTO);
        } else {
            log.warn("订单：[{}]渠道[{}]暂未开通分账能力，不进入分账队列", orderId, onlinePayChannel);
            return;
        }
    }

    private List<BillProfitSharing> buildBillProfitSharingV2(Long tenantId, Long orderId, String profitSharingNo, Map<String, BigDecimal> accountDimensionMap, PaymentDTO paymentDTO) {
        // 根据角色进行分组
        Set<String> accountInfoSet = accountDimensionMap.keySet();
        // 商户appId
        String appId = getAppId(tenantId);
        // 支付单交易流水号
        String transactionId = paymentDTO.getTransactionId();
        // 先预处理下分账金额，供应商和品牌方之间可能存在倒挂的情况
        preProcessProfitSharing(accountDimensionMap);
        // 支付渠道信息
        Integer onlinePayChannel = paymentDTO.getOnlinePayChannel();
        List<BillProfitSharing> billProfitSharingList = Lists.newArrayListWithCapacity(3);
        for (String accountInfo : accountInfoSet) {
            BigDecimal profitSharingPrice = accountDimensionMap.get(accountInfo);
            if (profitSharingPrice.compareTo(ZERO) <= 0) {
                log.info("accountInfo:{}本次分账金额为:{}元，将不参与分账", accountInfo, profitSharingPrice);
                continue;
            }
            BillProfitSharing billProfitSharing = new BillProfitSharing();
            billProfitSharing.setTenantId(tenantId);
            billProfitSharing.setOrderId(orderId);
            billProfitSharing.setAppId(appId);
            billProfitSharing.setType(WxAccountTypeEnum.PROPRIETARY.getDesc());
            Long accountId = getAccountId(accountInfo);
            String merchantNo = getMerchantNo(tenantId, accountInfo, paymentDTO);
            if (merchantNo == null) {
                throw new ProviderException(String.format("分账单:%s未查询到商户", profitSharingNo));
            }
            billProfitSharing.setAccount(merchantNo);
            billProfitSharing.setHuifuId(merchantNo);
            billProfitSharing.setTransactionId(transactionId);
            billProfitSharing.setOutTradeNo(profitSharingNo);
            billProfitSharing.setPrice(profitSharingPrice);
            billProfitSharing.setStatus(ProfitSharingResultEnum.WAITING.getStatus());
            billProfitSharing.setDescription("分给商户" + accountId);
            billProfitSharing.setReceiverTenantId(accountId);
            billProfitSharing.setCreateTime(LocalDateTime.now());
            billProfitSharing.setBusinessType(ProfitSharingBusinessType.FORWARD_DIRECTION.getCode());
            Integer accountType = getAccountType(accountInfo);
            billProfitSharing.setAccountType(accountType);
            billProfitSharing.setProfitSharingChannel(onlinePayChannel);
            billProfitSharingList.add(billProfitSharing);
        }
        log.info("发起分账记录：{}", JSONObject.toJSONString(billProfitSharingList));
        return billProfitSharingList;
    }

    private String getMerchantNo(Long tenantId, String accountInfo, PaymentDTO paymentDTO) {
        String[] split = accountInfo.split("-");
        Integer accountType = Integer.valueOf(split[0]);
        Long accountId = Long.valueOf(split[1]);
        Integer onlinePayChannel = paymentDTO.getOnlinePayChannel();
        List<HuiFuAccount> huiFuAccounts = huiFuAccountService.queryByAccountIdsAndChannel(tenantId, Lists.newArrayList(accountId), accountType, onlinePayChannel);
        if (!CollectionUtils.isEmpty(huiFuAccounts)) {
            // 取最新的一条
            huiFuAccounts.sort(Comparator.comparing(HuiFuAccount::getId).reversed());
            return huiFuAccounts.get(0).getHuifuId();
        }

        // 如果是租户自己
        if (Objects.equals(accountType, AccountTypeEnum.TENANT.getType())) {
            // 标准化的merchantNo是后续才加的
            if (paymentDTO.getMerchantNo() != null) {
                return paymentDTO.getMerchantNo();
            }
            // 以前的汇付单据还是查子表
            HuiFuPayment huiFuPayment = huifuPaymentService.selectByPaymentId(paymentDTO.getId());
            return huiFuPayment.getHuifuId();
        }
        return null;

//        // 非供应商（租户或者帆台平台）|| 鲜沐供应商
//        if (!Objects.equals(accountType, AccountTypeEnum.SUPPLIER.getType()) || Objects.equals(accountId, BillProfitSharingAccountIdEnum.SUPPLIER.getId())) {
//            TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(accountId);
//            return tenantAuthConnectionDTO.getHuifuId();
//        }

        // 如果是其他供应商 或者 接收方
//        List<HuiFuAccount> huiFuAccounts = huiFuAccountService.queryByAccountIds(tenantId, Lists.newArrayList(accountId), accountType);
//        if (CollectionUtils.isEmpty(huiFuAccounts)) {
//            throw new ProviderException("未查询到供应商汇付账号信息");
//        }
//        return huiFuAccounts.get(0).getHuifuId();
    }

    private Long getAccountId(String accountInfo) {
        String[] split = accountInfo.split("-");
        String accountId = split[1];
        return Long.valueOf(accountId);
    }

    private Integer getAccountType(String accountInfo) {
        String[] split = accountInfo.split("-");
        String accountId = split[0];
        return Integer.valueOf(accountId);
    }

    private String getPaymentTransactionId(Long orderId, Long tenantId) {
        PaymentDTO paymentDTO = paymentService.querySuccessPaymentInfoByOrderId(orderId, tenantId);
        return paymentDTO.getTransactionId();
    }

    private String getAppId(Long tenantId) {
        TenantAuthConnectionDTO tenantAuthConnectionDto = tenantService.queryTenantAuthConnection(tenantId);
        return tenantAuthConnectionDto.getAppId();
    }

    private void preProcessProfitSharing(Map<String, BigDecimal> accountDimensionMap) {
        String supplierAccountKey = getAccountKeyByAccountType(AccountTypeEnum.SUPPLIER.getType(), accountDimensionMap);
        String tenantAccountKey = getAccountKeyByAccountType(AccountTypeEnum.TENANT.getType(), accountDimensionMap);
        // 可能供应商和品牌方存在倒挂场景，这里重新设置下分账金额
        BigDecimal supplierProfitSharingPrice = accountDimensionMap.getOrDefault(supplierAccountKey, ZERO);
        BigDecimal tenantProfitSharingPrice = accountDimensionMap.getOrDefault(tenantAccountKey, ZERO);
        if (tenantProfitSharingPrice.compareTo(ZERO) <= 0) {
            accountDimensionMap.put(supplierAccountKey, supplierProfitSharingPrice.add(tenantProfitSharingPrice));
        }
    }

    private String getAccountKeyByAccountType(Integer accountType, Map<String, BigDecimal> accountDimensionMap) {
        return accountDimensionMap.entrySet()
                .stream()
                .filter(e -> e.getKey().startsWith(String.valueOf(accountType)))
                .findFirst()
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    private Map<String, BigDecimal> getProfitSharingAccountDimension(Long tenantId, Long orderId, String profitSharingNo) {
        // 查询订单分账明细
        HashMap<String, BigDecimal> resultMap = new HashMap<>();
        List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos = billProfitSharingService.queryByProfitSharingNo(tenantId, profitSharingNo);
        calculateTotalProfitSharing(billProfitSharingSnapshotDtos, resultMap);
        // 分账给个人就已经包含代仓了
        boolean personalFlag = billProfitSharingSnapshotDtos.stream().anyMatch(el -> Objects.equals(el.getType(), BillProfitSharingSnapshotTypeEnum.ALL.getCode()));
        if (personalFlag) {
            return resultMap;
        }
        // 查询代仓分账记录
        List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots = orderAgentSkuFeeRuleSnapshotMapper
                .queryByTenantIdAndOrderId(tenantId, orderId);
        calculateAgentProfitSharing(orderAgentSkuFeeRuleSnapshots, resultMap);
        return resultMap;
    }

    private void calculateAgentProfitSharing(List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots, HashMap<String, BigDecimal> resultMap) {
        if (CollectionUtils.isEmpty(orderAgentSkuFeeRuleSnapshots)) {
            return;
        }
        for (OrderAgentSkuFeeRuleSnapshot snap : orderAgentSkuFeeRuleSnapshots) {
            Integer accountType = AccountTypeEnum.getByTenantId(snap.getAccountId());
            String key = getKey(accountType, snap.getAccountId());
            BigDecimal currentTotal = resultMap.getOrDefault(key, BigDecimal.ZERO);
            BigDecimal newTotal = currentTotal.add(snap.getPrice());
            resultMap.put(key, newTotal);
        }
    }

    private void calculateTotalProfitSharing(List<BillProfitSharingSnapshotDTO> dataList, Map<String, BigDecimal> resultMap) {
        for (BillProfitSharingSnapshotDTO dto : dataList) {
            String key = getKey(dto.getAccountType(), dto.getAccountId());
            BigDecimal currentTotal = resultMap.getOrDefault(key, BigDecimal.ZERO);
            BigDecimal newTotal = currentTotal.add(dto.getProfitSharingPrice());
            resultMap.put(key, newTotal);
        }
    }

    private static String getKey(Integer accountType, Long accountId) {
        // 组合 accountType 和 accountId，用作 Map 的 key
        return accountType + "-" + accountId;
    }


    public void handleOrderProfitSharing(BillProfitSharingOrderDTO billProfitSharingOrderDto) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            log.info("订单id:[{}]开始处理分账", billProfitSharingOrderDto.getOrderId());
            boolean success = calculateProfitSharing(billProfitSharingOrderDto);
            // 提交事务
            transactionManager.commit(status);
            // 发送分账消息，去实际分账
            if (success) {
                sendProfitSharingMsg(billProfitSharingOrderDto.getProfitSharingNo(), billProfitSharingOrderDto.getOrderId());
                // 汇付有并发限制 简单处理睡2s（后续改用令牌桶加队列的形式）
                Thread.sleep(2000);
            }
        } catch (Exception e) {
            log.error("订单id:[{}]分账失败", billProfitSharingOrderDto.getOrderId(), e);
            transactionManager.rollback(status);
        }
    }

    /**
     * 校验订单是否需要分账
     *
     * @param billProfitSharingOrderDto
     * @return
     */
    private boolean chargeNeedProfitSharing(BillProfitSharingOrderDTO billProfitSharingOrderDto) {
        List<OrderAfterSaleResp> afterSaleDTOList = orderAfterSaleBusinessService.queryAfterSaleByOrderIdAndStatus(billProfitSharingOrderDto.getTenantId(), billProfitSharingOrderDto.getOrderId(), Lists.newArrayList(OrderAfterSaleStatusEnum.REFUNDING.getValue()));
        if (CollectionUtil.isNotEmpty(afterSaleDTOList)) {
            log.info("订单:{}，分账单:{}存在退款中的售后单，暂不分账", billProfitSharingOrderDto.getOrderId(), billProfitSharingOrderDto.getProfitSharingNo());
            // 有在退款中的就等下次在进行分账
            return false;
        }
        if (Objects.equals(billProfitSharingOrderDto.getProfitSharingMode(), ProfitSharingModeEnum.REAL_TIME.getCode())) {
            log.info("订单id:[{}]为实时分账订单，直接分账, 无需校验订单状态等", billProfitSharingOrderDto.getOrderId());
            return true;
        }

        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(billProfitSharingOrderDto.getOrderId()));
        boolean match = Boolean.FALSE;
        boolean isCombinedPay = Objects.equals(orderDTO.getPayType(), PayTypeEnum.COMBINED_PAY.getType());
        boolean noWarehouseOrderMeetSharingFlag = Objects.equals(orderDTO.getWarehouseType(), WarehouseTypeEnum.PROPRIETARY.getCode())
                && (Objects.equals(orderDTO.getStatus(), OrderStatusEnum.DELIVERING.getCode())
                || Objects.equals(orderDTO.getStatus(), OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode()))
                && !isCombinedPay;
        if (OrderStatusEnum.FINISHED.getCode().equals(orderDTO.getStatus()) || OrderStatusEnum.DELIVERING.getCode().equals(orderDTO.getStatus()) || noWarehouseOrderMeetSharingFlag) {
            match = true;
        }
        if (OrderStatusEnum.REFUNDED.getCode().equals(orderDTO.getStatus()) || OrderStatusEnum.CLOSED.getCode().equals(orderDTO.getStatus())) {
            match = true;
        }
        return match;
    }

    /**
     * @param billProfitSharingOrderDto
     * @description 根据当时的订单分账规则快照，计算各方的分账金额
     */
    @Override
    public boolean calculateProfitSharing(BillProfitSharingOrderDTO billProfitSharingOrderDto) {
//        校验订单状态是否是已完成/待收货
        boolean match = chargeNeedProfitSharing(billProfitSharingOrderDto);

        if (match) {
            Long orderId = billProfitSharingOrderDto.getOrderId();
            Long tenantId = billProfitSharingOrderDto.getTenantId();
            // 查询订单分账规则
            List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos = getProfitSharingRuleSnapshots(billProfitSharingOrderDto);
            // 处理代仓品费用
            List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots = handleAgentSkuProfitSharing(orderId,
                    tenantId);
            // 按照类型拆分
            // 自营商品
            List<BillProfitSharingSnapshotDTO> proprietaryProductBillProfitSharingSnapshotDtos = new ArrayList<>();
            // 运费
            List<BillProfitSharingSnapshotDTO> deliveryFeeBillProfitSharingSnapshotDtos = new ArrayList<>();
            // 供应商商品
            List<BillProfitSharingSnapshotDTO> supplyProductBillProfitSharingSnapshotDtos = new ArrayList<>();
            // 服务费
            List<BillProfitSharingSnapshotDTO> serviceFeeBillProfitSharingSnapshotDtos = new ArrayList<>();
            // 全部分给个人
            List<BillProfitSharingSnapshotDTO> allSharingToPersonalProfitSharingSnapshotDtos = new ArrayList<>();
            // 无仓商品
            List<BillProfitSharingSnapshotDTO> noWarehouseProductBillProfitSharingSnapshotDtos = new ArrayList<>();

            for (BillProfitSharingSnapshotDTO billProfitSharingSnapshotDto : billProfitSharingSnapshotDtos) {
                if (DeliveryTypeEnum.BRAND_DELIVERY.getCode().equals(billProfitSharingSnapshotDto.getDeliveryType()) && ProfitSharingRuleTypeEnum.PROPRIETARY_SKU.getCode()
                        .equals(billProfitSharingSnapshotDto.getProfitSharingType())) {
                    proprietaryProductBillProfitSharingSnapshotDtos.add(billProfitSharingSnapshotDto);
                } else if (ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()
                        .equals(billProfitSharingSnapshotDto.getProfitSharingType())) {
                    serviceFeeBillProfitSharingSnapshotDtos.add(billProfitSharingSnapshotDto);
                } else if (DeliveryTypeEnum.THIRD_DELIVERY.getCode().equals(billProfitSharingSnapshotDto.getDeliveryType()) && ProfitSharingRuleTypeEnum.SUPPLY_SKU.getCode()
                        .equals(billProfitSharingSnapshotDto.getProfitSharingType())) {
                    supplyProductBillProfitSharingSnapshotDtos.add(billProfitSharingSnapshotDto);
                } else if (ProfitSharingRuleTypeEnum.DELIVERY.getCode()
                        .equals(billProfitSharingSnapshotDto.getProfitSharingType())) {
                    deliveryFeeBillProfitSharingSnapshotDtos.add(billProfitSharingSnapshotDto);
                } else if (BillProfitSharingSnapshotTypeEnum.ALL.getCode()
                        .equals(billProfitSharingSnapshotDto.getType())) {
                    allSharingToPersonalProfitSharingSnapshotDtos.add(billProfitSharingSnapshotDto);
                } else if (ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType().equals(billProfitSharingSnapshotDto.getDeliveryType()) && ProfitSharingRuleTypeEnum.SUPPLY_SKU.getCode()
                        .equals(billProfitSharingSnapshotDto.getProfitSharingType())) {
                    noWarehouseProductBillProfitSharingSnapshotDtos.add(billProfitSharingSnapshotDto);
                }
            }

            // 自营仓商品分账金额计算
            if (!CollectionUtils.isEmpty(proprietaryProductBillProfitSharingSnapshotDtos)) {
                ProfitSharingCalculate proprietaryProductProfitSharingCalculate = OrderProfitSharingCalculateFactory.get(
                        proprietaryProductBillProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getDeliveryType(),
                        proprietaryProductBillProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getProfitSharingType(), null);
                proprietaryProductProfitSharingCalculate.profitSharingCalculate(proprietaryProductBillProfitSharingSnapshotDtos, null);
            }

            // 供应商商品分账金额计算
            if (!CollectionUtils.isEmpty(supplyProductBillProfitSharingSnapshotDtos)) {
                ProfitSharingCalculate supplyProductProfitSharingCalculate = OrderProfitSharingCalculateFactory.get(
                        supplyProductBillProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getDeliveryType(),
                        supplyProductBillProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getProfitSharingType(), null);
                supplyProductProfitSharingCalculate.profitSharingCalculate(supplyProductBillProfitSharingSnapshotDtos, null);
            }

            if (!CollectionUtils.isEmpty(deliveryFeeBillProfitSharingSnapshotDtos)) {
                // 运费分账金额计算
                ProfitSharingCalculate deliveryFeeProfitSharingCalculate = OrderProfitSharingCalculateFactory.get(
                        deliveryFeeBillProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getDeliveryType(),
                        deliveryFeeBillProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getProfitSharingType(), null);
                deliveryFeeProfitSharingCalculate.profitSharingCalculate(deliveryFeeBillProfitSharingSnapshotDtos, null);
            }

            if (!CollectionUtils.isEmpty(noWarehouseProductBillProfitSharingSnapshotDtos)) {
                Integer profitSharingDeliveryType = noWarehouseProductBillProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getDeliveryType();
                Integer profitSharingType = noWarehouseProductBillProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getProfitSharingType();
                // 无仓商品分账金额计算
                ProfitSharingCalculate noWarehouseProductProfitSharingCalculate = OrderProfitSharingCalculateFactory.get(profitSharingDeliveryType, profitSharingType, null);
                noWarehouseProductProfitSharingCalculate.profitSharingCalculate(noWarehouseProductBillProfitSharingSnapshotDtos, null);
            }

            // 订单金额全部分给个人
            if (!CollectionUtils.isEmpty(allSharingToPersonalProfitSharingSnapshotDtos)) {
                ProfitSharingCalculate sharingToPersonalProfitSharingCalculate = OrderProfitSharingCalculateFactory.get(null, null,
                        BillProfitSharingSnapshotTypeEnum.ALL.getCode());
                sharingToPersonalProfitSharingCalculate.profitSharingCalculate(allSharingToPersonalProfitSharingSnapshotDtos, null);
            }

            if (!CollectionUtils.isEmpty(serviceFeeBillProfitSharingSnapshotDtos)) {
                // 服务费分账金额计算（服务费最后一个算）
                List<BillProfitSharingSnapshotDTO> otherBillProfitSharingSnapshotDtos = new ArrayList<>();
                otherBillProfitSharingSnapshotDtos.addAll(proprietaryProductBillProfitSharingSnapshotDtos);
                otherBillProfitSharingSnapshotDtos.addAll(supplyProductBillProfitSharingSnapshotDtos);
                otherBillProfitSharingSnapshotDtos.addAll(deliveryFeeBillProfitSharingSnapshotDtos);
                otherBillProfitSharingSnapshotDtos.addAll(noWarehouseProductBillProfitSharingSnapshotDtos);
                otherBillProfitSharingSnapshotDtos.addAll(allSharingToPersonalProfitSharingSnapshotDtos);
                Map<Long, BigDecimal> accountProfitSharingPriceMap = profitSharingPriceByAccount(otherBillProfitSharingSnapshotDtos, orderAgentSkuFeeRuleSnapshots);
                ProfitSharingCalculate serviceFeeProfitSharingCalculate = OrderProfitSharingCalculateFactory.get(
                        serviceFeeBillProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getDeliveryType(),
                        serviceFeeBillProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getProfitSharingType(), null);
                serviceFeeProfitSharingCalculate.profitSharingCalculate(serviceFeeBillProfitSharingSnapshotDtos, accountProfitSharingPriceMap);
            }

            // 更新分账订单状态
            // 更新订单分账状态在分账中
            if (!BillProfitSharingOrderStatusEnum.CALCULATE_FINISHED.getStatus().equals(billProfitSharingOrderDto.getStatus())) {
                int i = billProfitSharingOrderService.updateStatusById(billProfitSharingOrderDto.getId(), BillProfitSharingOrderStatusEnum.CALCULATE_FINISHED.getStatus(), BillProfitSharingOrderStatusEnum.WAITING.getStatus());
                if (i != NumberConstant.ONE) {
                    log.error("{}订单分账状态更新失败", orderId);
                    throw new BizException("分账状态更新失败");
                }
            }
        } else {
            log.info("订单:{}不满足分账条件，结束分账", billProfitSharingOrderDto.getOrderId());
        }
        return match;
    }

    private List<BillProfitSharingSnapshotDTO> getProfitSharingRuleSnapshots(BillProfitSharingOrderDTO billProfitSharingOrderDto) {
        Long tenantId = billProfitSharingOrderDto.getTenantId();
        Long orderId = billProfitSharingOrderDto.getOrderId();
        String profitSharingNo = billProfitSharingOrderDto.getProfitSharingNo();
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotMapper.queryOrderSharingSnapshots(tenantId, orderId, null);
        boolean anyMatch = billProfitSharingSnapshots.stream().anyMatch(el -> Objects.nonNull(el.getProfitSharingNo()));
        if (anyMatch) {
            return billProfitSharingSnapshots.stream().filter(el -> Objects.equals(el.getProfitSharingNo(), profitSharingNo)).map(BillProfitSharingSnapshotConvert::toDTO).collect(Collectors.toList());
        }

        // 入口初始化分账单号和账户类型
        initProfitSharingNoAndAccountType(billProfitSharingOrderDto, billProfitSharingSnapshots);
        return BillProfitSharingSnapshotConvert.toDTOList(billProfitSharingSnapshots);
    }

    /**
     * 初始化分账单号和账户类型
     *
     * @param billProfitSharingOrderDto
     * @param billProfitSharingSnapshots
     */
    private void initProfitSharingNoAndAccountType(BillProfitSharingOrderDTO billProfitSharingOrderDto, List<BillProfitSharingSnapshot> billProfitSharingSnapshots) {
        String profitSharingNo = billProfitSharingOrderDto.getProfitSharingNo();
        if (StringUtils.isBlank(profitSharingNo)) {
            profitSharingNo = generateProfitSharingNo(billProfitSharingOrderDto.getOrderId());
            billProfitSharingOrderDto.setProfitSharingNo(profitSharingNo);
            billProfitSharingOrderService.updateProfitSharingNo(billProfitSharingOrderDto.getId(), profitSharingNo);
        }
        billProfitSharingSnapshots.forEach(el -> {
            el.setProfitSharingNo(billProfitSharingOrderDto.getProfitSharingNo());
            el.setAccountType(Optional.ofNullable(el.getAccountType()).orElse(AccountTypeEnum.getByTenantId(el.getAccountId())));
            billProfitSharingSnapshotMapper.updateByPrimaryKeySelective(el);
        });
    }

    /**
     * 按照账户计算分账金额
     */
    private Map<Long, BigDecimal> profitSharingPriceByAccount(
            List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos,
            List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots) {
        Map<Long, BigDecimal> accountProfitSharingPrice = new HashMap<>(NumberConstant.SIXTEEN);
        // 根据accountId分组
        for (BillProfitSharingSnapshotDTO billProfitSharingSnapshotDto : billProfitSharingSnapshotDtos) {
            if (accountProfitSharingPrice.containsKey(billProfitSharingSnapshotDto.getAccountId())) {
                BigDecimal profitSharingPrice = accountProfitSharingPrice.get(billProfitSharingSnapshotDto.getAccountId());
                accountProfitSharingPrice.put(billProfitSharingSnapshotDto.getAccountId(),
                        NumberUtil.add(profitSharingPrice, billProfitSharingSnapshotDto.getProfitSharingPrice()));
            } else {
                accountProfitSharingPrice.put(billProfitSharingSnapshotDto.getAccountId(),
                        NumberUtil.add(BigDecimal.ZERO, billProfitSharingSnapshotDto.getProfitSharingPrice()));
            }
        }
        // 分账给个人就已经包含代仓了
        boolean personalFlag = billProfitSharingSnapshotDtos.stream().anyMatch(el -> Objects.equals(el.getType(), BillProfitSharingSnapshotTypeEnum.ALL.getCode()));
        if (personalFlag) {
            return accountProfitSharingPrice;
        }

        if (!CollectionUtils.isEmpty(orderAgentSkuFeeRuleSnapshots)) {
            for (OrderAgentSkuFeeRuleSnapshot orderAgentSkuFeeRuleSnapshot : orderAgentSkuFeeRuleSnapshots) {
                if (accountProfitSharingPrice.containsKey(orderAgentSkuFeeRuleSnapshot.getAccountId())) {
                    BigDecimal profitSharingPrice = accountProfitSharingPrice
                            .get(orderAgentSkuFeeRuleSnapshot.getAccountId());
                    accountProfitSharingPrice.put(orderAgentSkuFeeRuleSnapshot.getAccountId(),
                            NumberUtil.add(profitSharingPrice, orderAgentSkuFeeRuleSnapshot.getPrice()));
                } else {
                    accountProfitSharingPrice.put(orderAgentSkuFeeRuleSnapshot.getAccountId(),
                            NumberUtil.add(BigDecimal.ZERO, orderAgentSkuFeeRuleSnapshot.getPrice()));
                }
            }
        }

        return accountProfitSharingPrice;
    }

    private void sendProfitSharingMsg(String profitSharingNo, Long orderId) {
        BillProfitSharingMessageDTO billProfitSharingMessageDTO = new BillProfitSharingMessageDTO();
        billProfitSharingMessageDTO.setOrderId(orderId);
        billProfitSharingMessageDTO.setProfitSharingNo(profitSharingNo);
        mqProducer.send(MQTopicConstant.TOPIC_BILL_PROFIT_SHARING, MqTagConstant.ORDER_PROFIT_SHARING, JSONObject.toJSONString(billProfitSharingMessageDTO));
        log.info("订单:{}，分账单号:{}分账消息发送", orderId, profitSharingNo);
    }

    /**
     * 生成分账单号
     *
     * @return 生成分账单号
     */
    private String generateProfitSharingNo(Long orderId) {
        return "PS" + System.currentTimeMillis() + orderId.hashCode() + StringUtils.orderRandomNum();
    }

    /**
     * 按件数处理代仓分账
     *
     * @param productAgentSkuFeeRuleDetailDTOS
     * @param orderAgentSkuFeeRuleSnapshots
     * @param agentSkuTotalAmount
     */
    private void updateAgentSkuProfitSharingPriceByAccount
    (List<ProductAgentSkuFeeRuleDetailDTO> productAgentSkuFeeRuleDetailDTOS, List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots, Integer
            agentSkuTotalAmount, BigDecimal productTotalPrice) {
        // 帆台
        TenantDTO fanTaiTenantDTO = tenantService.selectByType(TenantTypeEnum.FANTAI.getType());
        // 供应商
        TenantDTO supplierTenant = tenantService.selectByType(TenantTypeEnum.SUPPLIER.getType());
        // 临界点
        ProductAgentSkuFeeRuleDetailDTO recentProductAgentSkuFeeRule = null;
        Integer maxCount = NumberConstant.ZERO;
        for (ProductAgentSkuFeeRuleDetailDTO productAgentSkuFeeRuleDetailDTO : productAgentSkuFeeRuleDetailDTOS) {
            if (productAgentSkuFeeRuleDetailDTO.getCount().compareTo(agentSkuTotalAmount) <= NumberConstant.ZERO
                    && productAgentSkuFeeRuleDetailDTO.getCount().compareTo(maxCount) > NumberConstant.ZERO) {
                recentProductAgentSkuFeeRule = productAgentSkuFeeRuleDetailDTO;
                maxCount = productAgentSkuFeeRuleDetailDTO.getCount();
            }
        }

        BigDecimal agentSkuServiceFee =
                Objects.isNull(recentProductAgentSkuFeeRule) ? BigDecimal.ZERO : NumberUtil.mul(recentProductAgentSkuFeeRule.getAmount(), agentSkuTotalAmount);
        for (OrderAgentSkuFeeRuleSnapshot orderAgentSkuFeeRuleSnapshot : orderAgentSkuFeeRuleSnapshots) {
            // 供应商
            if (supplierTenant.getId().equals(orderAgentSkuFeeRuleSnapshot.getAccountId())) {
                orderAgentSkuFeeRuleSnapshot.setPrice(agentSkuServiceFee);
            } else if (fanTaiTenantDTO.getId().equals(orderAgentSkuFeeRuleSnapshot.getAccountId())) {
                orderAgentSkuFeeRuleSnapshot.setPrice(BigDecimal.ZERO);
            } else if (!fanTaiTenantDTO.getId().equals(orderAgentSkuFeeRuleSnapshot.getAccountId())) {
                orderAgentSkuFeeRuleSnapshot.setPrice(NumberUtil.sub(productTotalPrice, agentSkuServiceFee));
            }

            orderAgentSkuFeeRuleSnapshotMapper.updateByPrimaryKey(orderAgentSkuFeeRuleSnapshot);
        }
    }

    /**
     * 按比例处理代仓费用
     *
     * @param productAgentSkuFeeRuleDetailDTOS
     * @param orderAgentSkuFeeRuleSnapshots
     * @param productTotalPrice
     */
    private void updateAgentSkuProfitSharingPriceByRate
    (List<ProductAgentSkuFeeRuleDetailDTO> productAgentSkuFeeRuleDetailDTOS, List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots, BigDecimal
            productTotalPrice) {
        if (CollectionUtils.isEmpty(productAgentSkuFeeRuleDetailDTOS) || CollectionUtils.isEmpty(orderAgentSkuFeeRuleSnapshots)) {
            throw new ProviderException("分账异常，未查询到订单代仓快照规则");
        }

        // 租户维度的订单快照map
        Map<Long, OrderAgentSkuFeeRuleSnapshot> orderAgentSnapshotMap = orderAgentSkuFeeRuleSnapshots.stream().collect(Collectors.toMap(OrderAgentSkuFeeRuleSnapshot::getAccountId, item -> {
            item.setPrice(ZERO);
            return item;
        }));
        // 分配比例大于0的租户规则
        List<ProductAgentSkuFeeRuleDetailDTO> effectiveRuleList = productAgentSkuFeeRuleDetailDTOS.stream().filter(el -> el.getPercentage().compareTo(ZERO) > 0).collect(Collectors.toList());
        BigDecimal residuePrice = productTotalPrice;
        for (int i = 0; i < effectiveRuleList.size(); i++) {
            ProductAgentSkuFeeRuleDetailDTO rule = effectiveRuleList.get(i);
            if (rule.getPercentage() == null) {
                throw new ProviderException("代仓分账比例不能为空");
            }
            OrderAgentSkuFeeRuleSnapshot orderAgentSkuFeeRuleSnapshot = orderAgentSnapshotMap.get(rule.getMemberTenantId());
            // 最后一个采用末尾到减
            if (i == effectiveRuleList.size() - 1) {
                orderAgentSkuFeeRuleSnapshot.setPrice(residuePrice);
                continue;
            }
            BigDecimal percentage = rule.getPercentage();
            BigDecimal price = NumberUtil
                    .mul(productTotalPrice, NumberUtil.div(percentage, NumberConstant.HUNDRED))
                    .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
            orderAgentSkuFeeRuleSnapshot.setPrice(price);
            residuePrice = NumberUtil.sub(productTotalPrice, price);
        }
        // 更新价格
        orderAgentSnapshotMap.forEach((tenantId, orderAgentSnapshot) -> {
            log.info("租户：[{}]，订单：[{}]，分得代仓费用：[{}]元", orderAgentSnapshot.getTenantId(), orderAgentSnapshot.getOrderId(), orderAgentSnapshot.getPrice());
            orderAgentSkuFeeRuleSnapshotMapper.updateByPrimaryKeySelective(orderAgentSnapshot);
        });
    }

    @Override
    public void saveOrderProfitSharingRule(Long orderId) {
        log.info("订单：[{}]开始生成分账规则快照", orderId);
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        // 只有汇付才分账
        Set<Integer> usableProfitSharingChannel = OnlinePayChannelEnum.UsableProfitSharingChannel();
        if (!usableProfitSharingChannel.contains(orderDTO.getOnlinePayChannel())) {
            log.info("订单：[{}], 渠道：[{}], 暂未开通分账能力，不进入分账队列", orderId, OnlinePayChannelEnum.getDesc(orderDTO.getOnlinePayChannel()));
            return;
        }
        boolean isSelfCombinationProportion = tenantGrayConfig.selfCombinationProportion(orderDTO.getTenantId());
        boolean isProprietaryOrder = Objects.equals(orderDTO.getWarehouseType(), WarehouseTypeEnum.SELF_SUPPLY.getCode());
        if (!(isSelfCombinationProportion && isProprietaryOrder) && Objects.equals(orderDTO.getPayType(), PayTypeEnum.COMBINED_PAY.getType())) {
            saveCombinedPayOrderProfitSharingRule(orderDTO);
            return;
        }

        // 无仓订单保存规则
        saveNoWarehouseOrderProfitSharingRule(orderDTO);

        // 非无仓根据订单保存
        saveOrderProfitSharingRule(orderDTO);
        log.info("订单：[{}]分账规则快照结束", orderId);
    }

    private void saveCombinedPayOrderProfitSharingRule(OrderResp orderDTO) {
        Long orderId = orderDTO.getId();
        BillProfitSharingOrder billProfitSharingOrder = new BillProfitSharingOrder();
        billProfitSharingOrder.setOrderId(orderId);
        billProfitSharingOrder.setTenantId(orderDTO.getTenantId());
        billProfitSharingOrder.setStatus(BillProfitSharingOrderStatusEnum.WAITING.getStatus());
        billProfitSharingOrder.setProfitSharingNo(generateProfitSharingNo(orderId));
        BillProfitSharingConfig billProfitSharingConfig = billProfitSharingConfigService.queryByTenantId(orderDTO.getTenantId());
        billProfitSharingOrder.setProfitSharingMode(billProfitSharingConfig == null || Objects.equals(billProfitSharingConfig.getProfitSharingMode(), ProfitSharingModeEnum.DELAY.getCode()) ? ProfitSharingModeEnum.DELAY.getCode() : ProfitSharingModeEnum.REAL_TIME.getCode());
        billProfitSharingOrderService.save(billProfitSharingOrder);

        // 组合支付 全部分给品牌方个人
        ProfitSharingSnapshotBuilder builder = new ProfitSharing4PersonalSnapshotsBuilder();
        builder.build(billProfitSharingOrder, Collections.emptyList());
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = builder.getBillProfitSharingSnapshots();
        billProfitSharingSnapshotMapper.batchInsert(billProfitSharingSnapshots);
    }

    private void saveNoWarehouseOrderProfitSharingRule(OrderResp orderDTO) {
        if (!Objects.equals(orderDTO.getWarehouseType(), com.cosfo.ordercenter.client.common.WarehouseTypeEnum.PROPRIETARY.getCode())) {
            return;
        }
        Long orderId = orderDTO.getId();
        Long tenantId = orderDTO.getTenantId();
        List<OrderItemAndSnapshotResp> orderItemSnapshots = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderId));
        Set<Long> supplierIds = orderItemSnapshots.stream().map(OrderItemAndSnapshotResp::getSupplierTenantId).collect(Collectors.toSet());
        // 查询汇付账户信息
        List<HuiFuAccount> huiFuAccounts = huiFuAccountService.queryByAccountIdsAndChannel(tenantId, supplierIds, AccountTypeEnum.SUPPLIER.getType(), orderDTO.getOnlinePayChannel());
        Map<Long, Integer> huifuAccountMap = huiFuAccounts.stream()
                .sorted(Comparator.comparing(HuiFuAccount::getId).reversed())
                .collect(Collectors.toMap(HuiFuAccount::getAccountId, HuiFuAccount::getSharingSwitch, (v1, v2) -> v1));
        List<BillProfitSharingRule> billProfitSharingRules = billProfitSharingRuleMapper.selectByTenantIdAndDeliveryType(tenantId, ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType());
        if (CollectionUtils.isEmpty(billProfitSharingRules)) {
            throw new I18nProviderException("租户:{0}，分账规则为空，请检查配置", tenantId);
        }

        boolean isCombinedPay = Objects.equals(orderDTO.getPayType(), PayTypeEnum.COMBINED_PAY.getType());
        BillProfitSharingConfig billProfitSharingConfig = billProfitSharingConfigService.queryByTenantId(tenantId);
        boolean isDelayMode = billProfitSharingConfig == null || Objects.equals(billProfitSharingConfig.getProfitSharingMode(), ProfitSharingModeEnum.DELAY.getCode());

        for (Long supplierId : supplierIds) {
            // 构建分账规则快照 每个供应商都是一次分账
            BillProfitSharingOrder billProfitSharingOrder = buildBillProfitSharingOrder(tenantId, orderId, supplierId, ProfitSharingRuleTypeEnum.SUPPLY_SKU.getCode(), isDelayMode);
            billProfitSharingOrderService.save(billProfitSharingOrder);

            Integer sharingSwitch = huifuAccountMap.getOrDefault(supplierId, ProfitSharingSwitchEnum.CLOSE.getCode());
            // 如果是组合支付则关闭分账给其他方 只分自己
            if (isCombinedPay) {
                sharingSwitch = ProfitSharingSwitchEnum.CLOSE.getCode();
            }
            // 如果要分账只给品牌方，则对规则进行过滤
            List<BillProfitSharingRule> rules = checkIfPersonal(sharingSwitch, billProfitSharingRules);

            // 无仓分账规则
            ProfitSharingSnapshotBuilder builder = new ProfitSharing4NoWarehouseGoodsSnapshotBuilder();
            builder.build(billProfitSharingOrder, rules);
            List<BillProfitSharingSnapshot> billProfitSharingSnapshots = builder.getBillProfitSharingSnapshots();

            // 手续费分账规则
            ProfitSharingSnapshotBuilder serviceFeeSnapshotBuilder = new ProfitSharing4ServiceChargeSnapshotBuilder();
            serviceFeeSnapshotBuilder.build(billProfitSharingOrder, rules);
            billProfitSharingSnapshots.addAll(serviceFeeSnapshotBuilder.getBillProfitSharingSnapshots());

            billProfitSharingSnapshotMapper.batchInsert(billProfitSharingSnapshots);
            log.info("订单:{}，供应商:{}，分账单号:{}保存成功", orderId, supplierId, billProfitSharingOrder.getProfitSharingNo());
        }

        // 订单运费也进行快照保存
        saveNoWarehouseOrderProfitSharingRule4DeliveryFee(orderId, tenantId, billProfitSharingRules, isDelayMode);
    }

    private List<BillProfitSharingRule> checkIfPersonal(Integer sharingSwitch, List<BillProfitSharingRule> billProfitSharingRules) {
        if (Objects.equals(sharingSwitch, ProfitSharingSwitchEnum.CLOSE.getCode())) {
            return billProfitSharingRules.stream().filter(el -> Objects.equals(el.getAccountId(), el.getTenantId())).collect(Collectors.toList());
        }
        return billProfitSharingRules;
    }

    private void saveNoWarehouseOrderProfitSharingRule4DeliveryFee(Long orderId, Long tenantId, List<BillProfitSharingRule> billProfitSharingRules, boolean isDelayMode) {
        List<BillProfitSharingSnapshot> deliveryFeeSnapshots = Lists.newArrayListWithCapacity(2);

        // 运费分账快照
        BillProfitSharingOrder deliveryProfitSharingOrder = buildBillProfitSharingOrder(tenantId, orderId, null, ProfitSharingRuleTypeEnum.DELIVERY.getCode(), isDelayMode);
        billProfitSharingOrderService.save(deliveryProfitSharingOrder);

        // 运费分账规则
        ProfitSharingSnapshotBuilder deliveryFeeSnapshotBuilder = new ProfitSharing4DeliveryFeeSnapshotBuilder();
        deliveryFeeSnapshotBuilder.build(deliveryProfitSharingOrder, billProfitSharingRules);
        deliveryFeeSnapshots.addAll(deliveryFeeSnapshotBuilder.getBillProfitSharingSnapshots());
        // 手续费分账规则
        ProfitSharingSnapshotBuilder serviceFeeSnapshotBuilder = new ProfitSharing4ServiceChargeSnapshotBuilder();
        serviceFeeSnapshotBuilder.build(deliveryProfitSharingOrder, billProfitSharingRules);
        deliveryFeeSnapshots.addAll(serviceFeeSnapshotBuilder.getBillProfitSharingSnapshots());

        // 批量插入
        billProfitSharingSnapshotMapper.batchInsert(deliveryFeeSnapshots);
        log.info("订单:{}，分账单号:{}，保存成功", orderId, deliveryProfitSharingOrder.getProfitSharingNo());
    }

    private BillProfitSharingOrder buildBillProfitSharingOrder(Long tenantId, Long id, Long supplierId, Integer profitSharingType, boolean isDelayMode) {
        BillProfitSharingOrder billProfitSharingOrder = new BillProfitSharingOrder();
        billProfitSharingOrder.setOrderId(id);
        billProfitSharingOrder.setTenantId(tenantId);
        billProfitSharingOrder.setStatus(isDelayMode ? BillProfitSharingOrderStatusEnum.INIT.getStatus() : BillProfitSharingOrderStatusEnum.WAITING.getStatus());
        billProfitSharingOrder.setSupplierId(supplierId);
        billProfitSharingOrder.setProfitSharingType(profitSharingType);
        billProfitSharingOrder.setProfitSharingNo(generateProfitSharingNo(id));
        billProfitSharingOrder.setProfitSharingMode(isDelayMode ? ProfitSharingModeEnum.DELAY.getCode() : ProfitSharingModeEnum.REAL_TIME.getCode());
        return billProfitSharingOrder;
    }


    private void saveOrderProfitSharingRule(OrderResp orderDTO) {
        Long orderId = orderDTO.getId();
        // 暂时非无仓的都进入分账队列，后续要优化
        if (!Objects.equals(orderDTO.getWarehouseType(), com.cosfo.ordercenter.client.common.WarehouseTypeEnum.PROPRIETARY.getCode())) {
            BillProfitSharingConfig billProfitSharingConfig = billProfitSharingConfigService.queryByTenantId(orderDTO.getTenantId());
            BillProfitSharingOrder billProfitSharingOrder = new BillProfitSharingOrder();
            billProfitSharingOrder.setOrderId(orderId);
            billProfitSharingOrder.setTenantId(orderDTO.getTenantId());
            billProfitSharingOrder.setStatus(BillProfitSharingOrderStatusEnum.WAITING.getStatus());
            billProfitSharingOrder.setProfitSharingNo(generateProfitSharingNo(orderId));
            billProfitSharingOrder.setProfitSharingMode(billProfitSharingConfig == null || Objects.equals(billProfitSharingConfig.getProfitSharingMode(), ProfitSharingModeEnum.DELAY.getCode()) ? ProfitSharingModeEnum.DELAY.getCode() : ProfitSharingModeEnum.REAL_TIME.getCode());
            billProfitSharingOrderService.save(billProfitSharingOrder);

            // 保存订单分账规则
            saveProfitSharingRuleRecordByOrder(billProfitSharingOrder, orderDTO);
        }
    }

    private void saveProfitSharingRuleRecordByOrder(BillProfitSharingOrder billProfitSharingOrder, OrderResp orderDTO) {
        // 汇付微信间连 汇付支付宝间连
        Integer payType = orderDTO.getPayType();
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(orderDTO.getTenantId());
        Integer profitSharingSwitch = ProfitSharingSwitchEnum.CLOSE.getCode();
        if (Objects.equals(payType, PayTypeEnum.COMBINED_PAY.getType())) {
            List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.querySuccessCombinedByOrderId(orderDTO.getTenantId(), orderDTO.getId());
            for (PaymentCombinedDetail combinedDetail : combinedDetails) {
                Set<Integer> usableProfitSharingChannel = OnlinePayChannelEnum.UsableProfitSharingChannel();
                if (usableProfitSharingChannel.contains(combinedDetail.getOnlinePayChannel())) {
                    payType = TradeTypeEnum.getPayTypeByTradeType(combinedDetail.getTradeType());
                    break;
                }
            }
        }

        if (Objects.equals(payType, PayTypeEnum.WECHAT_PAY.getType())) {
            Payment payment = paymentService.queryPayment(orderDTO.getId(), orderDTO.getTenantId());
            // 微信公众号支付订单(H5)
            if (TradeTypeEnum.getH5TradeType().contains(payment.getTradeType())) {
                profitSharingSwitch = tenantAuthConnectionDTO.getH5WechatIndirectSharingSwitch();
            } else {
                profitSharingSwitch = tenantAuthConnectionDTO.getWechatIndirectSharingSwitch();
            }
        } else if (Objects.equals(payType, PayTypeEnum.ALI_PAY.getType())) {
            profitSharingSwitch = tenantAuthConnectionDTO.getAliIndirectSharingSwitch();
        }

        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = new ArrayList<>();
        Integer profitSharingDeliveryType = ProfitSharingDeliveryTypeEnums.getByWarehouseType(orderDTO.getWarehouseType());
        List<BillProfitSharingRule> billProfitSharingRules = billProfitSharingRuleMapper.selectByTenantIdAndDeliveryType(billProfitSharingOrder.getTenantId(), profitSharingDeliveryType);
        if (CollectionUtils.isEmpty(billProfitSharingRules)) {
            throw new I18nProviderException("租户:{0}，分账规则为空，请检查配置", billProfitSharingOrder.getTenantId());
        }
        // 微信支付且分账关闭不记录分账规则明细，汇付支付记录规则明细，全部分给品牌方个人
        if (ProfitSharingSwitchEnum.CLOSE.getCode().equals(profitSharingSwitch)) {
            ProfitSharingSnapshotBuilder builder = new ProfitSharing4PersonalSnapshotsBuilder();
            builder.build(billProfitSharingOrder, billProfitSharingRules);
            billProfitSharingSnapshots = builder.getBillProfitSharingSnapshots();
            billProfitSharingSnapshotMapper.batchInsert(billProfitSharingSnapshots);
            return;
        }

        // 分账开启，记录每一项分账规则
        // 供应商分账规则
        List<OrderItemAndSnapshotResp> orderItemSnapshots = RpcResultUtil.handle(orderItemQueryProvider.queryByOrderId(orderDTO.getId()));
        boolean quotationGoodsFlag = orderItemSnapshots.stream().anyMatch(el -> Objects.equals(el.getGoodsType(), GoodsTypeEnum.QUOTATION_TYPE.getCode()));
        if (Objects.equals(profitSharingDeliveryType, ProfitSharingDeliveryTypeEnums.THIRD_DELIVERY.getType()) && quotationGoodsFlag) {
            ProfitSharingSnapshotBuilder supplierGoodsSnapshotBuilder = new ProfitSharing4SupplierGoodsSnapshotBuilder();
            supplierGoodsSnapshotBuilder.build(billProfitSharingOrder, billProfitSharingRules);
            billProfitSharingSnapshots.addAll(supplierGoodsSnapshotBuilder.getBillProfitSharingSnapshots());
        }

        // 自营仓分账规则
        if (Objects.equals(profitSharingDeliveryType, ProfitSharingDeliveryTypeEnums.BRAND_DELIVERY.getType())) {
            ProfitSharingSnapshotBuilder proprietaryProductSnapshotBuilder = new ProfitSharing4ProprietaryGoodsSnapshotBuilder();
            proprietaryProductSnapshotBuilder.build(billProfitSharingOrder, billProfitSharingRules);
            billProfitSharingSnapshots.addAll(proprietaryProductSnapshotBuilder.getBillProfitSharingSnapshots());
        }

        // 运费分账规则
        ProfitSharingSnapshotBuilder deliveryFeeSnapshotBuilder = new ProfitSharing4DeliveryFeeSnapshotBuilder();
        deliveryFeeSnapshotBuilder.build(billProfitSharingOrder, billProfitSharingRules);
        billProfitSharingSnapshots.addAll(deliveryFeeSnapshotBuilder.getBillProfitSharingSnapshots());

        // 服务费分账规则
        ProfitSharingSnapshotBuilder serviceFeeSnapshotBuilder = new ProfitSharing4ServiceChargeSnapshotBuilder();
        serviceFeeSnapshotBuilder.build(billProfitSharingOrder, billProfitSharingRules);
        billProfitSharingSnapshots.addAll(serviceFeeSnapshotBuilder.getBillProfitSharingSnapshots());
        billProfitSharingSnapshotMapper.batchInsert(billProfitSharingSnapshots);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void profitSharingFlow(Long orderId) {
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
        if (Objects.isNull(orderDTO)) {
            log.info("订单:{}不存在", orderId);
            return;
        }
        if (!Objects.equals(orderDTO.getOnlinePayChannel(), OnlinePayChannelEnum.HUIFU_PAY.getChannel())) {
            log.info("订单:{}暂未开通汇付分账能力，暂不处理", orderId);
            return;
        }
        billProfitSharingSnapshotMapper.resetPriceByOrderId(orderDTO.getTenantId(), orderDTO.getId());
        log.info("订单:{}清除历史分账金额完毕", orderId);

        List<BillProfitSharingOrder> billProfitSharingOrders = billProfitSharingOrderService.queryByTenantAndOrderId(orderDTO.getTenantId(), orderDTO.getId());
        if (CollectionUtils.isEmpty(billProfitSharingOrders)) {
            log.info("订单:{}无分账单据，不进行分账", orderId);
            return;
        }

        log.info("开始处理订单分账:{}", orderId);
        BillProfitSharingOrder billProfitSharingOrder = billProfitSharingOrders.get(0);
        BillProfitSharingOrderDTO billProfitSharingOrderDTO = BillProfitSharingOrderConvert.toBillProfitSharingOrderDTO(billProfitSharingOrder);
        boolean match = calculateProfitSharing(billProfitSharingOrderDTO);
        log.info("订单:{}分账计算完毕", orderId);

        // 实际分账
        log.info("订单:{}开始实际分账", orderId);
        if (match) {
            doProfitSharing(billProfitSharingOrderDTO.getProfitSharingNo());
        }
        log.info("订单:{}实际分账完毕", orderId);
    }

    @Override
    public void profitSharingFlowByNo(String profitSharingNo) {
        log.info("开始处理分账单:{}", profitSharingNo);
        BillProfitSharingOrder billProfitSharingOrder = billProfitSharingOrderService.queryByProfitSharingNo(profitSharingNo);
        if (Objects.isNull(billProfitSharingOrder)) {
            log.info("分账单号:{}不存在", profitSharingNo);
            return;
        }
        // 不等于初始化或者待分账过滤
        boolean allowProfitSharing = Objects.equals(billProfitSharingOrder.getStatus(), BillProfitSharingOrderStatusEnum.WAITING.getStatus())
                || Objects.equals(billProfitSharingOrder.getStatus(), BillProfitSharingOrderStatusEnum.INIT.getStatus());
        if (!allowProfitSharing) {
            log.info("分账单号:{}状态不匹配, 状态为:{}", profitSharingNo, BillProfitSharingOrderStatusEnum.getDescByStatus(billProfitSharingOrder.getStatus()));
            return;
        }
        BillProfitSharingOrderDTO billProfitSharingOrderDTO = BillProfitSharingOrderConvert.toBillProfitSharingOrderDTO(billProfitSharingOrder);
        boolean match = calculateProfitSharing(billProfitSharingOrderDTO);
        if (match) {
            doProfitSharing(profitSharingNo);
        }
        log.info("分账单:{}处理完毕", profitSharingNo);
    }

    @Override
    public void updateSnapshots(BillProfitSharingSnapshot billProfitSharingSnapshot) {
        if (billProfitSharingSnapshot == null) {
            return;
        }
        if (billProfitSharingSnapshot.getId() == null) {
            return;
        }
        billProfitSharingSnapshotMapper.updateByPrimaryKeySelective(billProfitSharingSnapshot);
    }

    @Override
    public boolean existsByOrderIds(Long tenantId, List<Long> orderIds) {
        return billProfitSharingSnapshotMapper.existsByOrderIds(tenantId, orderIds);
    }

    @Override
    public void generateBillProfitSharingSnapshots(Long tenantId, List<Long> orderIds) {
        BillProfitSharingConfig billProfitSharingConfig = billProfitSharingConfigService.queryByTenantId(tenantId);
        if (Objects.nonNull(billProfitSharingConfig) && Objects.equals(billProfitSharingConfig.getProfitSharingSwitch(), ProfitSharingSwitchEnum.CLOSE.getCode())) {
            log.info("租户:{}分账功能关闭，不生成分账规则, 订单ID:{}", tenantId, orderIds);
            return;
        }

        boolean existed = checkIfOrderProfitSharingRuleExist(tenantId, orderIds);
        if (existed) {
            log.info("订单:{}分账规则已存在，无需重复生成", orderIds);
            return;
        }
        for (Long orderId : orderIds) {
            saveOrderProfitSharingRule(orderId);
        }
    }

    private boolean checkIfOrderProfitSharingRuleExist(Long tenantId, List<Long> orderIds) {
        return existsByOrderIds(tenantId, orderIds);
    }

    @Override
    public void profitSharingByOrderIdsRightNow(Long tenantId, List<Long> orderIds) {
        List<BillProfitSharingOrderDTO> billProfitSharingOrders = billProfitSharingOrderService.queryByOrderIds(orderIds, tenantId);
        List<BillProfitSharingOrderDTO> realTimeProfitSharingOrders = billProfitSharingOrders
                .stream()
                .filter(el -> Objects.equals(el.getProfitSharingMode(), ProfitSharingModeEnum.REAL_TIME.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(realTimeProfitSharingOrders)) {
            log.info("订单:{}无实时分账单，不进行实时分账", orderIds);
            return;
        }
        log.info("开始处理订单实时分账:{}", orderIds);
        for (BillProfitSharingOrderDTO billProfitSharingOrderDTO : realTimeProfitSharingOrders) {
            try {
                profitSharingFlowByNo(billProfitSharingOrderDTO.getProfitSharingNo());
                // 汇付/智付有并发限制 简单处理睡2s（后续改用令牌桶加队列的形式）
                Thread.sleep(2000);
            } catch (Exception e) {
                log.error("订单:{}，分账单:{}，实时分账失败", billProfitSharingOrderDTO.getOrderId(), billProfitSharingOrderDTO.getProfitSharingNo(), e);
            }
        }
    }
}

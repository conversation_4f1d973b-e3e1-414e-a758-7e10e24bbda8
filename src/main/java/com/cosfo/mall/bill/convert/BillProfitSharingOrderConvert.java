package com.cosfo.mall.bill.convert;

import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/10
 */
public class BillProfitSharingOrderConvert {

    public static List<BillProfitSharingOrderDTO> convertToList(List<BillProfitSharingOrder> billProfitSharingOrders){
        if (billProfitSharingOrders == null) {
            return Collections.emptyList();
        }

        List<BillProfitSharingOrderDTO> billProfitSharingOrderDTOList = new ArrayList<>();
        for (BillProfitSharingOrder billProfitSharingOrder : billProfitSharingOrders) {
            billProfitSharingOrderDTOList.add(toBillProfitSharingOrderDTO(billProfitSharingOrder));
        }
        return billProfitSharingOrderDTOList;
    }

    public static BillProfitSharingOrderDTO toBillProfitSharingOrderDTO(BillProfitSharingOrder billProfitSharingOrder) {
        if (billProfitSharingOrder == null) {
            return null;
        }
        BillProfitSharingOrderDTO billProfitSharingOrderDTO = new BillProfitSharingOrderDTO();
        billProfitSharingOrderDTO.setId(billProfitSharingOrder.getId());
        billProfitSharingOrderDTO.setTenantId(billProfitSharingOrder.getTenantId());
        billProfitSharingOrderDTO.setOrderId(billProfitSharingOrder.getOrderId());
        billProfitSharingOrderDTO.setStatus(billProfitSharingOrder.getStatus());
        billProfitSharingOrderDTO.setCreateTime(billProfitSharingOrder.getCreateTime());
        billProfitSharingOrderDTO.setRetryNum(billProfitSharingOrder.getRetryNum());
        billProfitSharingOrderDTO.setSupplierId(billProfitSharingOrder.getSupplierId());
        billProfitSharingOrderDTO.setProfitSharingNo(billProfitSharingOrder.getProfitSharingNo());
        billProfitSharingOrderDTO.setProfitSharingMode(billProfitSharingOrder.getProfitSharingMode());
        return billProfitSharingOrderDTO;
    }
}

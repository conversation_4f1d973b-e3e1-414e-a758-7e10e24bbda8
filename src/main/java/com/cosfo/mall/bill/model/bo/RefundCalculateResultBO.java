package com.cosfo.mall.bill.model.bo;

import com.cosfo.mall.bill.model.po.BillProfitSharingRefundSnapshot;
import com.cosfo.mall.payment.model.dto.RefundAcctSplitDetailDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;

/**
 * @description:
 * @author: George
 * @date: 2023-07-25
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RefundCalculateResultBO {
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 售后单id
     */
    private Long orderAfterSaleId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 原始金额
     */
    private BigDecimal originPrice;

    /**
     * 帆台退款金额
     */
    private BigDecimal ftRefundPrice;

    /**
     * 帆台应退金额
     */
    private BigDecimal ftShouldRefundPrice;

    /**
     * 帆台实退金额
     */
    private BigDecimal ftActualRefundPrice;

    /**
     * 帆台最终退款金额
     */
    private BigDecimal ftFinalRefundPrice;

    /**
     * 供应商退款金额
     */
    private BigDecimal supplierRefundPrice;

    /**
     * 供应商应退金额
     */
    private BigDecimal supplierShouldRefundPrice;

    /**
     * 供应商实退金额
     */
    private BigDecimal supplierActualRefundPrice;

    /**
     * 供应商最终退款金额
     */
    private BigDecimal supplierFinalRefundPrice;

    /**
     * 租户退款金额
     */
    private BigDecimal tenantRefundPrice;

    /**
     * 租户应退金额
     */
    private BigDecimal tenantShouldRefundPrice;

    /**
     * 租户实退金额
     */
    private BigDecimal tenantActualRefundPrice;

    /**
     * 租户最终退款金额
     */
    private BigDecimal tenantFinalRefundPrice;

    /**
     * 租户运费退款金额
     */
    private BigDecimal tenantDeliveryRefundFee;

    /**
     * 供应商运费退款金额
     */
    private BigDecimal supplierDeliveryRefundFee;

    /**
     * 平台运费退款金额
     */
    private BigDecimal ftDeliveryRefundFee;

    /**
     * 退款分账金额
     */
    private ArrayList<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots;

    /**
     * 账户维度退款
     */
    private ArrayList<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOS;

    /**
     * 供应商id
     */
    private Long supplierId;
}

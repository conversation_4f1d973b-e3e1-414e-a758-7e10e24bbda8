package com.cosfo.mall.bill.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/24
 */
@Data
public class BillProfitSharingOrderDTO {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 分账状态 0、待分账 1、分账金额计算完毕 2、分账中 3、分账成功 4、初始化
     */
    private Integer status;

    /**
     * 重试次数
     */
    private Integer retryNum;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * @see com.cosfo.mall.common.constants.ProfitSharingRuleTypeEnum
     */
    private Integer profitSharingType;

    /**
     * 分账单号
     */
    private String profitSharingNo;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 分账模式 1、延迟 2、实时
     *
     * @see com.cosfo.mall.common.context.ProfitSharingModeEnum
     */
    private Integer profitSharingMode;
}

package com.cosfo.mall.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.message.client.enums.SMSTypeEnum;
import com.cosfo.message.client.enums.SmsPlatformCodeEnum;
import com.cosfo.message.client.enums.SmsSignEnum;
import com.cosfo.message.client.provider.SmsSendProvider;
import com.cosfo.message.client.req.SmsBySceneIdReq;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 发送短信
 */
@Component
@Slf4j
public class SmsFacade {
    @DubboReference
    private SmsSendProvider smsSendProvider;
    @NacosValue(value = "${mc.smsPlatformCode:CHUANGLAN,ALi}", autoRefreshed = true)
    private String smsPlatformCode;

    /**
     * 发送验证码
     * @param phone
     * @param templateArg
     */
    public void sendSms( String phone,  List<String> templateArg) {
        SmsBySceneIdReq req = new SmsBySceneIdReq ();
        req.setSceneCode("SEND_VERIFICATION_CODE");
        req.setSmsType(SMSTypeEnum.NOTIFY);
        req.setPhone(phone);
        req.setTemplateArg(templateArg);

        if(StringUtils.isNotBlank (smsPlatformCode)) {
            List<SmsPlatformCodeEnum> collect = Splitter.on (",").splitToStream (smsPlatformCode).filter (StringUtils::isNotBlank).map (SmsPlatformCodeEnum::of).filter (Objects::nonNull).collect (Collectors.toList ());
            if(CollectionUtil.isNotEmpty (collect)) {
                req.setPlatformCodeEnums (collect);
            }
        }
        req.setSignEnum (SmsSignEnum.KSF);
        DubboResponse<Void> response = smsSendProvider.sendBySceneId (req);
        if (!response.isSuccess ()) {
            throw new ProviderException ("发送短信失败：" + response.getMsg ());
        }
    }

}

package com.cosfo.mall.order.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.BillProfitSharingSnapshotTypeEnum;
import com.cosfo.mall.common.constants.OnlinePayChannelEnum;
import com.cosfo.mall.common.constants.PayTypeEnum;
import com.cosfo.mall.common.context.shard.ProfitSharingDeliveryTypeEnums;
import com.cosfo.mall.order.service.ProfitSharingCalculate;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.PaymentCombinedOrderDetail;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentCombinedOrderDetailService;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述: 订单金额全部分账给个人
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/4
 */
@Service
@Slf4j
public class SharingToPersonalProfitSharingCalculate implements ProfitSharingCalculate {
    @Resource
    private BillProfitSharingService billProfitSharingService;
    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;
    @Resource
    private PaymentService paymentService;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;
    @Resource
    private PaymentCombinedOrderDetailService paymentCombinedOrderDetailService;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderItemQueryProvider orderItemQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;

    @Override
    public boolean support(Integer deliveryType, Integer profitSharingRuleType, Integer type) {
        return BillProfitSharingSnapshotTypeEnum.ALL.getCode().equals(type);
    }

    @Override
    public void profitSharingCalculate(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, Map<Long, BigDecimal> accountProfitSharingPriceMap) {
        // 查询订单
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(billProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getOrderId()));

        BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO = billProfitSharingSnapshotDtos.get(NumberConstant.ZERO);
        String profitSharingNo = billProfitSharingSnapshotDTO.getProfitSharingNo();
        BillProfitSharingOrder billProfitSharingOrder = billProfitSharingOrderService.queryByProfitSharingNo(profitSharingNo);

        // 组合支付
        if (Objects.equals(orderDTO.getPayType(), PayTypeEnum.COMBINED_PAY.getType())) {
            processCombinedPayToPersonal(billProfitSharingSnapshotDTO);
            return;
        }

        if (Objects.equals(billProfitSharingSnapshotDTO.getDeliveryType(), ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType())) {
            processNoWarehouseToPersonal(billProfitSharingOrder, billProfitSharingSnapshotDTO);
            return;
        }
        // 查询已经完成的售后单
        OrderAfterSaleQueryReq afterSaleQueryReq = new OrderAfterSaleQueryReq();
        afterSaleQueryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        afterSaleQueryReq.setOrderIds(Lists.newArrayList(orderDTO.getId()));
        afterSaleQueryReq.setTenantId(orderDTO.getTenantId());
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(afterSaleQueryReq));
        BigDecimal afterSalePrice = BigDecimal.ZERO;
        // 计算售后退款金额
        if (!CollectionUtils.isEmpty(afterSaleDTOList)) {
            afterSalePrice = afterSaleDTOList.stream().map(OrderAfterSaleResp::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 剩余金额
        BigDecimal residuePrice = NumberUtil.sub(orderDTO.getTotalPrice(), afterSalePrice);
        billProfitSharingSnapshotDTO.setOriginPrice(orderDTO.getTotalPrice());
        billProfitSharingSnapshotDTO.setProfitSharingPrice(residuePrice);
        billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDTO);
    }

    private void processCombinedPayToPersonal(BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO) {
        Long tenantId = billProfitSharingSnapshotDTO.getTenantId();
        Long orderId = billProfitSharingSnapshotDTO.getOrderId();
        log.info("开始处理组合支付分账给个人:{}", billProfitSharingSnapshotDTO);
        PaymentDTO paymentDTO = paymentService.querySuccessByOrderId(tenantId, orderId);
        String paymentNo = paymentDTO.getPaymentNo();
        List<PaymentCombinedDetail> details = paymentCombinedDetailService.selectByCombinedPaymentNo(paymentNo);
        Set<Integer> usableProfitSharingChannel = OnlinePayChannelEnum.UsableProfitSharingChannel();
        PaymentCombinedDetail combinedDetail = details.stream().filter(el -> usableProfitSharingChannel.contains(el.getOnlinePayChannel()))
                .findFirst().orElseThrow(() -> new ProviderException("未查询到现结支付子单"));
        Long id = combinedDetail.getId();
        List<PaymentCombinedOrderDetail> paymentCombinedOrderDetails = paymentCombinedOrderDetailService.selectByCombinedDetailIds(tenantId, Collections.singletonList(id));
        Optional<PaymentCombinedOrderDetail> matchOrder = paymentCombinedOrderDetails.stream()
                .filter(el -> Objects.equals(el.getOrderId(), orderId))
                .findFirst();
        if (matchOrder.isPresent()) {
            PaymentCombinedOrderDetail el = matchOrder.get();
            billProfitSharingSnapshotDTO.setOriginPrice(el.getTotalPrice());
            billProfitSharingSnapshotDTO.setProfitSharingPrice(el.getTotalPrice());
            billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDTO);
            log.info("组合支付分账给个人:{}", billProfitSharingSnapshotDTO);
        } else {
            billProfitSharingSnapshotDTO.setOriginPrice(BigDecimal.ZERO);
            billProfitSharingSnapshotDTO.setProfitSharingPrice(BigDecimal.ZERO);
            billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDTO);
            log.info("未找到匹配订单，组合支付分账金额设为0");
        }
        log.info("处理组合支付分账给个人完毕:{}", billProfitSharingSnapshotDTO);
    }

    private void processNoWarehouseToPersonal(BillProfitSharingOrder billProfitSharingOrder, BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO) {
        // 查订单信息
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotDTOList = getOrderInfo(billProfitSharingOrder.getTenantId(), billProfitSharingOrder.getOrderId(), billProfitSharingOrder.getSupplierId());
        BigDecimal totalPrice = orderItemAndSnapshotDTOList.stream().map(OrderItemAndSnapshotResp::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<Long> orderItemIds = orderItemAndSnapshotDTOList.stream().map(OrderItemAndSnapshotResp::getOrderItemId).collect(Collectors.toList());

        // 查售后单信息
        List<OrderAfterSaleResp> afterSaleDTOList = getOrderAfterSaleInfo(orderItemIds);
        BigDecimal refundPrice = afterSaleDTOList.stream().map(el -> NumberUtil.sub(el.getTotalPrice(), el.getDeliveryFee())).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal residuePrice = NumberUtil.sub(totalPrice, refundPrice);
        billProfitSharingSnapshotDTO.setOriginPrice(totalPrice);
        billProfitSharingSnapshotDTO.setProfitSharingPrice(residuePrice);
        billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDTO);
        log.info("处理无仓库分账给个人完毕:{}", billProfitSharingSnapshotDTO);
    }

    private List<OrderAfterSaleResp> getOrderAfterSaleInfo(List<Long> orderItemIds) {
        OrderAfterSaleQueryReq afterSaleQueryReq = new OrderAfterSaleQueryReq();
        afterSaleQueryReq.setOrderItemIds(orderItemIds);
        afterSaleQueryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        return RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(afterSaleQueryReq));
    }

    private List<OrderItemAndSnapshotResp> getOrderInfo(Long tenantId, Long orderId, Long supplierId) {
        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setTenantId(tenantId);
        orderItemQueryReq.setOrderIds(Collections.singletonList(orderId));
        orderItemQueryReq.setSupplierIds(supplierId == null ? null : Collections.singletonList(supplierId));
        return RpcResultUtil.handle(orderItemQueryProvider.queryOrderItemList(orderItemQueryReq));
    }
}

package com.cosfo.mall.order.converter;

import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.model.po.Refund;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/21
 */
@Mapper
public interface RefundConvert {
    RefundConvert INSTANCE = Mappers.getMapper(RefundConvert.class);

    /**
     * 转化为RefundDTO
     *
     * @param refund
     * @return
     */
    RefundDTO toRefundDTO(Refund refund);
}

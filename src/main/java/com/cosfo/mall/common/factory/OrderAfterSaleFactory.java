//package com.cosfo.mall.common.factory;
//
//import com.cosfo.aftersale.common.context.OrderAfterSaleServiceTypeEnum;
//import com.cosfo.mall.order.executor.*;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 14:44
// */
//@Component
//public class OrderAfterSaleFactory {
//
//    @Resource
//    private RefundAfterSaleExecutor refundAfterSaleExecutor;
//
//    @Resource
//    private ReturnRefundAfterSaleExecutor returnRefundAfterSaleExecutor;
//
//    @Resource
//    private ExchangeAfterSaleExecutor exchangeAfterSaleExecutor;
//
//    @Resource
//    private ResendAfterSaleExecutor resendAfterSaleExecutor;
//
//    private Map<Integer, AfterSaleAbstractExecutor> afterSaleAbstractExecutorMap;
//
//    @PostConstruct
//    private void initAfterSaleStrategyMap() {
//        afterSaleAbstractExecutorMap = new HashMap<>(8);
//        afterSaleAbstractExecutorMap.put(OrderAfterSaleServiceTypeEnum.REFUND.getType(), refundAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(OrderAfterSaleServiceTypeEnum.REFUND_ENTER_BILL.getType(), refundAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(OrderAfterSaleServiceTypeEnum.RETURN_REFUND.getType(), returnRefundAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(OrderAfterSaleServiceTypeEnum.RETURN_REFUND_ENTER_BILL.getType(), returnRefundAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(OrderAfterSaleServiceTypeEnum.EXCHANGE.getType(), exchangeAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(OrderAfterSaleServiceTypeEnum.RESEND.getType(), resendAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(OrderAfterSaleServiceTypeEnum.BALANCE.getType(), refundAfterSaleExecutor);
//        afterSaleAbstractExecutorMap.put(OrderAfterSaleServiceTypeEnum.RETURN_REFUND_BALANCE.getType(), returnRefundAfterSaleExecutor);
//    }
//
//    public AfterSaleAbstractExecutor getAfterSaleExecutor(Integer type) {
//        return afterSaleAbstractExecutorMap.get(type);
//    }
//}

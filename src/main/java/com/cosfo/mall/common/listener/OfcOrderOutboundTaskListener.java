package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.FulfillmentOutBoundTaskCreateMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *  三方仓订单出库任务创建消息
 *  更新订单状态【待出库】更新为【待收货】
 *
 * @author: xiaowk
 * @date: 2023/7/22 下午9:23
 */
@Slf4j
@Component
@MqListener(topic = "topic_ofc_outbound_task_notice",
        consumerGroup = MqGroupConstant.GID_OFC_TO_COSFO_OUTBOUND_TASK_CREATED_NOTICE,
        tag = "tag_saas_order_outbound_task_created_notice"
)
public class OfcOrderOutboundTaskListener extends AbstractMqListener<FulfillmentOutBoundTaskCreateMessage> {

    @Resource
    private OrderService orderService;

    @Override
    public void process(FulfillmentOutBoundTaskCreateMessage fulfillmentOutBoundTaskCreateMessage) {
        log.info("rocketmq 收到订单出库任务创建消息，消息内容：{}", JSONObject.toJSONString(fulfillmentOutBoundTaskCreateMessage));

        orderService.updateOrderByOutboundTaskCreate(fulfillmentOutBoundTaskCreateMessage.getOutOrderNoList());
    }
}

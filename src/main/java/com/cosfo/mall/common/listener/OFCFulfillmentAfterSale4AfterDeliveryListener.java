package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleUpdateReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.FulfillmentInfo;
import net.summerfarm.ofc.client.common.message.cosfo.FulfillmentOrderResultMessageDTO;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 配送后售后，履约单创建结果消息
 * - 只处理三方仓订单，更新售后单的城配仓号和配送日期
 */
@Slf4j
@Component
@MqListener(topic = "topic_ofc_fulfillment_order_result",
        consumerGroup = MqGroupConstant.GID_OFC_TO_COSFO_AFTER_DELIVERY_AFTER_SALE_ORDER,
        tag = "tag_ofc_saas_after_delivery_after_sale_order"
)
public class OFCFulfillmentAfterSale4AfterDeliveryListener extends AbstractMqListener<FulfillmentOrderResultMessageDTO> {

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;

    @Override
    public void process(FulfillmentOrderResultMessageDTO msg) {
        log.info("rocketmq 收到OFC配送后售后通知消息，消息内容：{}", JSONObject.toJSONString(msg));
        String sourceOrderNo = msg.getAfterSaleOrderNo();
        if (StringUtil.isEmpty(sourceOrderNo)) {
            return;
        }
        if (!msg.isOfcHandleSuccess()) {
            log.error("rocketmq 收到OFC配送后售后通知消息，处理失败，消息内容：{}", JSONObject.toJSONString(msg));
            return;
        }

        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByNos(Lists.newArrayList(sourceOrderNo)));

        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            log.error("rocketmq 收到OFC配送后售后通知消息，售后订单为空，消息内容：{}", JSONObject.toJSONString(msg));
            return;
        }

        OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);

        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(afterSaleDTO.getOrderId()));
        // 非三方仓订单售后，不处理
        if (!OrderEnums.WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderDTO.getWarehouseType())) {
            log.info("rocketmq 收到OFC配送后售后通知消息, 不是三方仓订单，不处理, orderAfterSale:{}", JSON.toJSONString(afterSaleDTO));
            return;
        }

        FulfillmentInfo fulfillmentInfo = msg.getFulfillmentInfo();
        if (fulfillmentInfo == null || (fulfillmentInfo.getFulfillmentDate() == null && fulfillmentInfo.getStoreNo() == null)) {
            log.error("rocketmq 收到OFC配送后售后通知消息，售后单{} 配送信息为空", msg, new Exception("OFC配送后售后通知消息为空"));
            return;
        }

        OrderAfterSaleUpdateReq update = new OrderAfterSaleUpdateReq();
        update.setId(afterSaleDTO.getId());
        update.setRecycleTime(Objects.isNull(fulfillmentInfo.getFulfillmentDate()) ? null : fulfillmentInfo.getFulfillmentDate().atStartOfDay());
        update.setStoreNo(fulfillmentInfo.getStoreNo());
        RpcResultUtil.handle(orderAfterSaleCommandProvider.updateById(update));
    }
}

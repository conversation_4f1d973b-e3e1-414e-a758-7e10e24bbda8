package com.cosfo.mall.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/9 19:05
 * @Description: 计算运费时的常量
 */
public class DeliveryConstant {

    /**
     * 已到货售后
     */
    public static final Integer AFTER_SALE_TYPE_AFTER = 0;

    /**
     * 未到货退款
     */
    public static final Integer AFTER_SALE_TYPE_BEFORE = 1;

    /**
     * 参与计算的售后服务类型
     * 1-退款  2-退款录入账单 7-录入余额
     * 这3种类型都属于送货前售后（订单中心迁移后，使用枚举代替）
     */
    public static final List<Integer> SERVICE_TYPE = Arrays.asList(1,2,7);

}

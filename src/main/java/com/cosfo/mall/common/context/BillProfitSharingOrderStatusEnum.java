package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-08-01
 **/
@NoArgsConstructor
@AllArgsConstructor
@Getter
public enum BillProfitSharingOrderStatusEnum {
    /**
     * 待分账
     */
    WAITING(0, "待分账"),

    /**
     * 分账金额计算完毕
     */
    CALCULATE_FINISHED(1, "分账金额计算完毕"),

    /**
     * 分账中
     */
    PROCESSING(2, "分账中"),

    /**
     * 已完成
     */
    FINISHED(3, "已完成"),

    /**
     * 初始化
     */
    INIT(4, "初始化"),

    /**
     * 部分完成
     */
    PART_FINISHED(5, "部分完成"),

    /**
     * 失败
     */
    FAILED(6, "分账失败");

    private Integer status;
    private String desc;

    public static String getDescByStatus(Integer status) {
        for (BillProfitSharingOrderStatusEnum value : BillProfitSharingOrderStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value.getDesc();
            }
        }
        return null;
    }
}

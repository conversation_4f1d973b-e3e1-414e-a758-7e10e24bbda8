package com.cosfo.mall.common.utils.binlog.impl;

import com.cosfo.mall.common.context.binlog.DbTableName;
import com.cosfo.mall.common.utils.binlog.observer.BinlogObserver;
import com.cosfo.mall.common.utils.binlog.observer.refund.RefundObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class RefundDmlServiceImpl extends DbTableDmlServiceImpl {

    @Resource
    private List<RefundObserver> refundObservers;

    @Override
    public String getTableDmlName() {
        return DbTableName.REFUND;
    }


    @Override
    protected List<? extends BinlogObserver> getObservers() {
        return refundObservers;
    }
}

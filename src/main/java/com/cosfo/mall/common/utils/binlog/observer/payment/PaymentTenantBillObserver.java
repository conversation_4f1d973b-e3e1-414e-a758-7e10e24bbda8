package com.cosfo.mall.common.utils.binlog.observer.payment;

import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.tenant.service.TenantBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class PaymentTenantBillObserver implements PaymentObserver {

    @Resource
    private TenantBillService tenantBillService;

    @Override
    public void onUpdate(Map<String, String> newData, Map<String, String> oldData) {

        Long paymentId = Long.valueOf(newData.get("id"));
        Integer newStatus = Optional.ofNullable(newData.get("status")).map(Integer::valueOf).orElse(null);

        // 当支付状态转变为支付成功后,更新tenant bill表
        if (PaymentEnum.Status.SUCCESS.getCode().equals(newStatus)
                || PaymentEnum.Status.DUPLICATE_SUCCESS.getCode().equals(newStatus)) {
            updateTenantBillTable(paymentId);
        }
    }


    /**
     * 监听payment表.
     * 当支付状态从待支付转变为成功支付后,异步更新tenant bill表
     */
    private void updateTenantBillTable(Long paymentId) {
        log.info("开始更新tenant bill表，paymentId: {}", paymentId);
        tenantBillService.createTenantBillForPayment(paymentId);
    }
}

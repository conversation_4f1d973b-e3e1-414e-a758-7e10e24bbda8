package com.cosfo.mall.common.utils.binlog.impl;

import cn.hutool.core.lang.Pair;
import com.cosfo.mall.common.context.binlog.BinlogEventEnum;
import com.cosfo.mall.common.context.binlog.DbTableName;
import com.cosfo.mall.common.mq.model.DtsModelBO;
import com.cosfo.mall.common.utils.binlog.DbTableDmlService;
import com.cosfo.mall.common.utils.binlog.DtsModelHandler;
import com.cosfo.mall.openapi.service.OrderNotifyBizService;
import com.cosfo.mall.storeinventory.service.StoreInventoryService;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * order表监听
 * @author: xiaowk
 * @time: 2023/7/11 下午4:27
 */
@Slf4j
@Component
public class OrderDmlServiceImpl implements DbTableDmlService {

    @Resource
    private OrderNotifyBizService orderNotifyBizService;
    @Resource
    private StoreInventoryService storeInventoryService;

    @Override
    public String getTableDmlName() {
        return DbTableName.COSFO_TABLE_ORDER;
    }

    @Override
    public void tableDml(DtsModelBO dtsModelBo) {
        if (!Objects.equals(BinlogEventEnum.UPDATE.getEvent(), dtsModelBo.getType())) {
            return;
        }
        if (CollectionUtils.isEmpty(dtsModelBo.getData())) {
            return;
        }

        List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData(dtsModelBo);
        for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
            Map<String, String> dataMap = pair.getKey();
            Map<String, String> oldMap = pair.getValue();

            // 处理柠季发货信息回传，如果失败，抛异常，mq消息重试
            dealData(dataMap, oldMap);

            // 处理本来自提信息回传
            selfLiftFinishedNotify(dataMap);

            // 订单完成，更新门店库存
            storeInventoryInboundByOrder(dataMap);
        }
    }

    private void dealData(Map<String, String> dataMap, Map<String, String> oldMap) {

        String customerOrderId = dataMap.get("customer_order_id");
        Integer status = Integer.valueOf(dataMap.get("status"));
        // status 4-待收货，且是外部api下单，customerOrderId，需要回传发货信息
        if (OrderStatusEnum.DELIVERING.getCode().equals(status) && StringUtils.isNotBlank(customerOrderId)) {
            Long orderId = Long.valueOf(dataMap.get("id"));
            orderNotifyBizService.notifyOrderDelivering(orderId);
            return;
        }
    }

    private void selfLiftFinishedNotify(Map<String, String> dataMap) {
        try {
            String customerOrderId = dataMap.get("customer_order_id");
            Integer status = Integer.valueOf(dataMap.get("status"));
            Long orderId = Long.valueOf(dataMap.get("id"));
            // status 4-待收货，且是外部api下单，customerOrderId,若是自提订单，需要回传配送完成信息
            if (OrderStatusEnum.DELIVERING.getCode().equals(status) && StringUtils.isNotBlank(customerOrderId)) {
                orderNotifyBizService.selfLiftFinishedNotifyThirdPart(orderId);
                return;
            }
            log.info("selfLiftFinishedNotify 非自提完成的外部api订单 orderId={}", orderId);
        } catch (Exception e) {
            log.error("dts消费处理order数据错误 selfLiftFinishedNotify e", e);
        }
    }


    private void storeInventoryInboundByOrder(Map<String, String> dataMap){
        try {
            Integer status = Integer.valueOf(dataMap.get("status"));
            Long orderId = Long.valueOf(dataMap.get("id"));
            // status 5-已完成
            if (OrderStatusEnum.FINISHED.getCode().equals(status)) {
//                storeInventoryService.orderFinishStoreInventoryInbound(orderId);
                storeInventoryService.orderFinishStoreInventoryInboundWithBill(orderId);
            }
        } catch (Exception e) {
            log.error("dts消费处理order数据错误 storeInventoryInboundByOrder ", e);
        }
    }

}

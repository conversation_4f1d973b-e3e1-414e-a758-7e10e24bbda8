package com.cosfo.mall.merchant.repository;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalanceChangeRecord;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 门店余额表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface MerchantStoreBalanceRepository extends IService<MerchantStoreBalance> {


    /**
     * 变更余额
     * @param id
     * @param changeBalance
     * @return
     */
    int decreaseBalance(Long id, BigDecimal changeBalance);

    /**
     * 增加余额
     * @param id
     * @param changeBalance
     * @return
     */
    int increaseBalance(Long id, BigDecimal changeBalance);

    /**
     * 查询现金账户
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    MerchantStoreBalance queryCashAccountByStoreId(Long tenantId, Long storeId);

    /**
     * 查询非现金账户
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    List<MerchantStoreBalance> queryNonCashAccountByStoreId(Long tenantId, Long storeId);

    /**
     * 根据门店id查询
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    List<MerchantStoreBalance> queryByStoreId(Long tenantId, Long storeId);

    /**
     * 冻结金额
     *
     * @param id
     * @param changeBalance
     * @return
     */
    int freezeBalance(Long id, BigDecimal changeBalance);

    /**
     * 扣减冻结金额
     *
     * @param id
     * @param changeBalance
     * @return
     */
    int decreaseFreezeBalance(Long id, BigDecimal changeBalance);

    /**
     * 根据门店id和资金账户id查询
     *
     * @param tenantId
     * @param storeId
     * @param fundAccountId
     * @return
     */
    MerchantStoreBalance queryByStoreAndFundAccountId(Long tenantId, Long storeId, Long fundAccountId);

    /**
     * 查询非现金账户（加锁）
     *
     * @param tenantId
     * @param storeId
     * @return
     */
    List<MerchantStoreBalance> queryNonCashAccountByStoreIdForUpdate(Long tenantId, Long storeId);
}

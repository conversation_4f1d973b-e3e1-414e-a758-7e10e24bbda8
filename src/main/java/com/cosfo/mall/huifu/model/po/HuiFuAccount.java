package com.cosfo.mall.huifu.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 汇付账户表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Getter
@Setter
@TableName("hui_fu_account")
public class HuiFuAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 汇付商户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 汇付商户id
     */
    @TableField("huifu_id")
    private String huifuId;

    /**
     * 账户id（租户id/供应商id）
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 账户类型 0、平台 1、供应商 2、租户
     */
    @TableField("account_type")
    private Integer accountType;

    /**
     * 分账开关 0、关闭 1、开启
     */
    @TableField("sharing_switch")
    private Integer sharingSwitch;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 渠道 1、汇付 2、智付
     */
    @TableField("online_channel")
    private Integer onlineChannel;


}

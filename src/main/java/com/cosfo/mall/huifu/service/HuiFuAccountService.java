package com.cosfo.mall.huifu.service;

import com.cosfo.mall.huifu.model.po.HuiFuAccount;

import java.util.Collection;
import java.util.List;

/**
 * @description: 汇付账户服务层
 * @author: <PERSON>
 * @date: 2024-01-04
 **/
public interface HuiFuAccountService {

    /**
     * 根据账号ids查询
     *
     * @param accountIds
     * @return
     */
    List<HuiFuAccount> queryByAccountIds(Long tenantId, Collection<Long> accountIds, Integer accountType);

    /**
     * 根据账号ids和支付渠道查询
     *
     * @param tenantId
     * @param accountIds
     * @param accountType
     * @param onlineChannel
     * @return
     */
    List<HuiFuAccount> queryByAccountIdsAndChannel(Long tenantId, Collection<Long> accountIds, Integer accountType, Integer onlineChannel);
}

package com.cosfo.mall.huifu.service.impl;

import com.cosfo.mall.huifu.model.po.HuiFuAccount;
import com.cosfo.mall.huifu.repository.HuiFuAccountRepository;
import com.cosfo.mall.huifu.service.HuiFuAccountService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2024-01-04
 **/
@Service
public class HuiFuAccountServiceImpl implements HuiFuAccountService {

    @Resource
    private HuiFuAccountRepository huiFuAccountRepository;

    @Override
    public List<HuiFuAccount> queryByAccountIds(Long tenantId, Collection<Long> accountIds, Integer accountType) {
        return huiFuAccountRepository.queryByAccountIds(tenantId, accountIds, accountType);
    }

    @Override
    public List<HuiFuAccount> queryByAccountIdsAndChannel(Long tenantId, Collection<Long> accountIds, Integer accountType, Integer onlineChannel) {
        return huiFuAccountRepository.queryByAccountIdsAndChannel(tenantId, accountIds, accountType, onlineChannel);
    }
}

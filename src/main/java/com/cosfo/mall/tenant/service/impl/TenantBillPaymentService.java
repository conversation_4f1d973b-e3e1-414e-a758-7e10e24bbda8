package com.cosfo.mall.tenant.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.OnlinePayChannelEnum;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.common.context.TenantBillTypeEnum;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.mapper.PaymentCombinedOrderDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.PaymentCombinedOrderDetail;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentCombinedOrderDetailService;
import com.cosfo.mall.tenant.model.po.TenantBill;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TenantBillPaymentService {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;
    @Resource
    private PaymentCombinedOrderDetailMapper paymentCombinedOrderDetailMapper;
    @Resource
    private PaymentCombinedOrderDetailService paymentCombinedOrderDetailService;

    public List<TenantBill> createTenantBillForPayment(Long paymentId) {
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        if (payment == null) {
            log.error("支付单不存在, paymentId: {}", paymentId);
            return Collections.emptyList();
        }

        List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentId(paymentId);
        if (CollectionUtils.isEmpty(paymentItems)) {
            log.error("支付单:{}没有支付项", paymentId);
            return Collections.emptyList();
        }

        // 组合支付
        if (Objects.equals(payment.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
            return handleCombinedPayment(payment);
        }

        // 需要手续费的渠道
        if (isChannelNeedFee(payment.getOnlinePayChannel())) {
            return handlePaymentWithFee(payment, paymentItems);
        }

        // 普通支付
        return handleNormalPayment(paymentItems);
    }

    /**
     * 判断支付渠道是否需要计算手续费
     */
    private boolean isChannelNeedFee(Integer onlinePayChannel) {
        return OnlinePayChannelEnum.HUIFU_PAY.getChannel().equals(onlinePayChannel) ||
                OnlinePayChannelEnum.DIN_PAY.getChannel().equals(onlinePayChannel);
    }

    /**
     * 统一处理需要手续费的支付渠道
     */
    private List<TenantBill> handlePaymentWithFee(Payment payment, List<PaymentItem> paymentItems) {
        log.info("开始处理需要手续费的支付单，paymentId: {}, channel: {}",
                payment.getId(), payment.getOnlinePayChannel());

        // 获取费率和总手续费
        FeeCalculationResult feeResult = calculateFeeForChannel(payment);
        if (feeResult == null) {
            log.info("支付单{}费率信息获取失败", payment.getId());
            return Collections.emptyList();
        }

        // 分摊手续费到订单
        return distributeFeeToOrders(payment, paymentItems, feeResult);
    }

    /**
     * 生成组合支付交易流水
     *
     * @param payment
     * @return
     */
    private List<TenantBill> handleCombinedPayment(Payment payment) {
        // 查询组合支付明细
        List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.selectByCombinedPaymentNo(payment.getPaymentNo());
        if (CollectionUtils.isEmpty(combinedDetails)) {
            log.error("组合支付单:{}没有组合明细", payment.getId());
            return Collections.emptyList();
        }

        // 收集所有组合明细ID
        List<Long> combinedDetailIds = combinedDetails.stream()
                .map(PaymentCombinedDetail::getId)
                .collect(Collectors.toList());

        // 查询组合支付订单明细
        Long tenantId = payment.getTenantId();
        List<PaymentCombinedOrderDetail> orderDetails = paymentCombinedOrderDetailService.selectByCombinedDetailIds(tenantId, combinedDetailIds);
        if (CollectionUtils.isEmpty(orderDetails)) {
            log.error("组合支付单:{}没有订单明细", payment.getId());
            return Collections.emptyList();
        }

        // 获取费率
        BigDecimal feeRate = payment.getFeeRate();
        if (feeRate == null) {
            log.info("组合支付单{}费率为空,需查询费率", payment.getId());
            return Collections.emptyList();
        }

        // 按组合明细处理手续费
        List<TenantBill> bills = new ArrayList<>();
        for (PaymentCombinedDetail detail : combinedDetails) {
            // 判断是否需要手续费的渠道
            if (!isChannelNeedFee(detail.getOnlinePayChannel())) {
                // 余额支付等无手续费渠道：直接生成无手续费的账单
                List<PaymentCombinedOrderDetail> detailOrders = orderDetails.stream()
                        .filter(od -> od.getCombinedDetailId().equals(detail.getId()))
                        .collect(Collectors.toList());
                bills.addAll(detailOrders.stream()
                        .map(el -> {
                            return createBillForCombinedOrderDetail(el, detail.getTradeType());
                        })
                        .collect(Collectors.toList()));
            } else {
                // 需要手续费的渠道：计算手续费
                bills.addAll(handleCombinedDetailWithFee(payment, detail, orderDetails, feeRate));
            }
        }

        return bills;
    }

    private List<TenantBill> handleCombinedDetailWithFee(Payment payment, PaymentCombinedDetail detail,
                                                         List<PaymentCombinedOrderDetail> orderDetails, BigDecimal feeRate) {
        // 获取该组合明细的订单
        List<PaymentCombinedOrderDetail> detailOrders = orderDetails.stream()
                .filter(od -> od.getCombinedDetailId().equals(detail.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(detailOrders)) {
            log.error("组合支付明细:{}没有订单明细", detail.getId());
            return Collections.emptyList();
        }

        // 根据渠道计算总手续费
        BigDecimal totalFeeAmount = calculateCombinedDetailFee(payment, detail, feeRate);

        // 分摊手续费到各个订单
        return distributeCombinedFeeToOrders(detailOrders, detail, totalFeeAmount, feeRate);
    }

    /**
     * 计算组合支付明细的总手续费
     */
    private BigDecimal calculateCombinedDetailFee(Payment payment, PaymentCombinedDetail detail, BigDecimal feeRate) {
        Integer channel = detail.getOnlinePayChannel();
        Set<Integer> usableProfitSharingChannel = OnlinePayChannelEnum.UsableProfitSharingChannel();
        if (usableProfitSharingChannel.contains(channel)) {
            // 现结支付：优先使用已存在的手续费
            HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(
                    new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, payment.getId()));

            String feeAmountStr = huiFuPayment.getFeeAmount();
            if (feeAmountStr != null) {
                BigDecimal totalFee = new BigDecimal(feeAmountStr);
                log.info("组合支付汇付明细{}使用已存在的手续费: {}", detail.getId(), totalFee);
                return totalFee;
            }
        } else if (OnlinePayChannelEnum.DIN_PAY.getChannel().equals(channel)) {
            if (payment.getFeeAmount() != null) {
                log.info("组合支付智付明细{}使用已存在的手续费: {}", detail.getId(), payment.getFeeAmount());
                return payment.getFeeAmount();
            }
        }

        // 通过费率计算总手续费
        BigDecimal totalFee = NumberUtil.mul(detail.getTotalPrice(),
                        NumberUtil.div(feeRate, NumberConstant.HUNDRED))
                .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);

        log.info("组合支付明细{}通过费率计算手续费: {} * {} = {}",
                detail.getId(), detail.getTotalPrice(), feeRate, totalFee);

        return totalFee;
    }

    /**
     * 将组合支付的手续费分摊到各个订单
     */
    private List<TenantBill> distributeCombinedFeeToOrders(List<PaymentCombinedOrderDetail> detailOrders,
                                                           PaymentCombinedDetail detail,
                                                           BigDecimal totalFeeAmount,
                                                           BigDecimal feeRate) {
        BigDecimal feeAccumulated = BigDecimal.ZERO;
        List<TenantBill> bills = new ArrayList<>();

        for (int i = 0; i < detailOrders.size(); i++) {
            PaymentCombinedOrderDetail orderDetail = detailOrders.get(i);
            TenantBill bill = createBillForCombinedOrderDetail(orderDetail, detail.getTradeType());

            if (i != detailOrders.size() - 1) {
                // 非最后一个订单：按费率计算手续费
                BigDecimal fee = NumberUtil.mul(orderDetail.getTotalPrice(),
                                NumberUtil.div(feeRate, NumberConstant.HUNDRED))
                        .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
                bill.setFeeAmount(fee);
                feeAccumulated = feeAccumulated.add(fee);

                if (feeAccumulated.compareTo(totalFeeAmount) > 0) {
                    log.error("手续费累计大于总手续费,组合明细:{}, 累计:{}, 总计:{}",
                            detail.getId(), feeAccumulated, totalFeeAmount);
                    throw new ProviderException("手续费累计错误");
                }
            } else {
                // 最后一个订单：用总手续费减去累计值
                bill.setFeeAmount(totalFeeAmount.subtract(feeAccumulated));
            }
            bills.add(bill);
        }

        return bills;
    }

    private TenantBill createBillForCombinedOrderDetail(PaymentCombinedOrderDetail orderDetail, String tradeType) {
        // 查询订单信息
        OrderResp order = RpcResultUtil.handle(orderQueryProvider.queryById(orderDetail.getOrderId()));
        TenantBill bill = new TenantBill();
        bill.setType(TenantBillTypeEnum.INCOME.getType());
        bill.setTenantId(order.getTenantId());
        bill.setStoreId(order.getStoreId());
        bill.setBillPrice(orderDetail.getTotalPrice());
        bill.setRecordNo(order.getOrderNo());
        bill.setPaymentType(TradeTypeEnum.getPayTypeByTradeType(tradeType));
        bill.setOnlinePayChannel(order.getOnlinePayChannel());
        bill.setCreateTime(orderDetail.getCreateTime() != null ? orderDetail.getCreateTime() : LocalDateTime.now());
        return bill;
    }

    private List<TenantBill> handleNormalPayment(List<PaymentItem> paymentItems) {
        return paymentItems.stream().map(this::createBill).collect(Collectors.toList());
    }

    private TenantBill createBill(PaymentItem paymentItem) {
        OrderResp order = RpcResultUtil.handle(orderQueryProvider.queryById(paymentItem.getOrderId()));
        TenantBill bill = new TenantBill();
        bill.setType(TenantBillTypeEnum.INCOME.getType());
        bill.setTenantId(order.getTenantId());
        bill.setStoreId(order.getStoreId());
        bill.setBillPrice(paymentItem.getOrderPrice());
        bill.setRecordNo(order.getOrderNo());
        bill.setPaymentType(order.getPayType());
        bill.setOnlinePayChannel(order.getOnlinePayChannel());
        if (null != paymentItem.getCreateTime()) {
            bill.setCreateTime(paymentItem.getCreateTime());
        } else {
            bill.setCreateTime(LocalDateTime.now());
        }

        return bill;
    }

    /**
     * 根据支付渠道计算费用
     */
    private FeeCalculationResult calculateFeeForChannel(Payment payment) {
        Integer channel = payment.getOnlinePayChannel();

        if (OnlinePayChannelEnum.HUIFU_PAY.getChannel().equals(channel)) {
            return calculateHuiFuFee(payment);
        } else if (OnlinePayChannelEnum.DIN_PAY.getChannel().equals(channel)) {
            return calculateDinPayFee(payment);
        }

        log.error("不支持的支付渠道: {}", channel);
        return null;
    }

    /**
     * 计算汇付支付费用
     */
    private FeeCalculationResult calculateHuiFuFee(Payment payment) {
        BigDecimal feeRate = payment.getFeeRate();
        if (feeRate == null) {
            log.info("汇付支付单{}费率为空,需查询费率", payment.getId());
            return null;
        }

        HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(
                new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, payment.getId()));

        String feeAmount = huiFuPayment.getFeeAmount();
        BigDecimal totalFeeAmount;

        // 优先使用已存在的手续费，不存在则通过费率计算
        if (feeAmount != null) {
            totalFeeAmount = new BigDecimal(feeAmount);
            log.info("汇付支付单{}使用已存在的手续费: {}", payment.getId(), totalFeeAmount);
        } else {
            totalFeeAmount = NumberUtil.mul(payment.getTotalPrice(),
                            NumberUtil.div(feeRate, NumberConstant.HUNDRED))
                    .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
            log.info("汇付支付单{}通过费率计算手续费: {} * {} = {}",
                    payment.getId(), payment.getTotalPrice(), feeRate, totalFeeAmount);
        }

        return new FeeCalculationResult(feeRate, totalFeeAmount);
    }

    /**
     * 计算智付支付费用
     */
    private FeeCalculationResult calculateDinPayFee(Payment payment) {
        BigDecimal feeRate = payment.getFeeRate();
        if (feeRate == null) {
            log.info("智付支付单{}费率为空,需查询费率", payment.getId());
            return null;
        }

        // 智付支付目前主要通过费率计算手续费
        BigDecimal feeAmount = payment.getFeeAmount();
        BigDecimal totalFeeAmount;
        // 优先使用已存在的手续费，不存在则通过费率计算
        if (feeAmount != null) {
            totalFeeAmount = feeAmount;
            log.info("智付支付单{}使用已存在的手续费: {}", payment.getId(), totalFeeAmount);
        } else {
            totalFeeAmount = NumberUtil.mul(payment.getTotalPrice(),
                            NumberUtil.div(feeRate, NumberConstant.HUNDRED))
                    .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
            log.info("智付支付单{}通过费率计算手续费: {} * {} = {}",
                    payment.getId(), payment.getTotalPrice(), feeRate, totalFeeAmount);
        }

        log.info("智付支付单{}通过费率计算手续费: {} * {} = {}",
                payment.getId(), payment.getTotalPrice(), feeRate, totalFeeAmount);

        return new FeeCalculationResult(feeRate, totalFeeAmount);
    }

    /**
     * 将手续费分摊到各个订单
     */
    private List<TenantBill> distributeFeeToOrders(Payment payment, List<PaymentItem> paymentItems,
                                                   FeeCalculationResult feeResult) {
        List<Long> orderIds = paymentItems.stream().map(PaymentItem::getOrderId).collect(Collectors.toList());
        List<OrderResp> orders = RpcResultUtil.handle(orderQueryProvider.queryByIds(orderIds));

        BigDecimal totalFeeAmount = feeResult.getTotalFeeAmount();
        BigDecimal feeAccumulated = BigDecimal.ZERO;
        List<TenantBill> bills = new ArrayList<>();

        for (int i = 0; i < paymentItems.size(); i++) {
            PaymentItem paymentItem = paymentItems.get(i);
            OrderResp order = orders.get(i);
            TenantBill bill = createBill(paymentItem);

            if (i != paymentItems.size() - 1) {
                // 非最后一个订单：按订单金额比例计算手续费
                BigDecimal fee = NumberUtil.mul(order.getTotalPrice(),
                                NumberUtil.div(feeResult.getFeeRate(), NumberConstant.HUNDRED))
                        .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
                bill.setFeeAmount(fee);
                feeAccumulated = feeAccumulated.add(fee);

                if (feeAccumulated.compareTo(totalFeeAmount) > 0) {
                    log.error("手续费累计大于总手续费,支付单:{}", payment.getId());
                    throw new ProviderException("手续费累计错误");
                }
            } else {
                // 最后一个订单：用总手续费减去累计值
                bill.setFeeAmount(totalFeeAmount.subtract(feeAccumulated));
            }
            bills.add(bill);
        }

        return bills;
    }

    /**
     * 费率计算结果
     */
    private static class FeeCalculationResult {
        private BigDecimal feeRate;
        private BigDecimal totalFeeAmount;

        public FeeCalculationResult(BigDecimal feeRate, BigDecimal totalFeeAmount) {
            this.feeRate = feeRate;
            this.totalFeeAmount = totalFeeAmount;
        }

        public BigDecimal getFeeRate() {
            return feeRate;
        }

        public BigDecimal getTotalFeeAmount() {
            return totalFeeAmount;
        }
    }
}

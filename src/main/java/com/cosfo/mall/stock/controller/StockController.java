package com.cosfo.mall.stock.controller;

import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.stock.service.StockService;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 描述: 库存处理对外控制类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/24
 */
@RestController
@RequestMapping("stock")
public class StockController {
    @Resource
    private StockService stockService;

    /**
     * 售后释放库存
     *
     * @param saleDTO
     * @return
     */
    @Deprecated
    @RequestMapping(value = "after/sale/unlock/stock", method = RequestMethod.POST)
    public ResultDTO unlockStockByAfterSale(@RequestBody OrderAfterSaleResp saleDTO) {
        stockService.unlockStockByAfterSale(saleDTO);
        return ResultDTO.success();
    }
}
